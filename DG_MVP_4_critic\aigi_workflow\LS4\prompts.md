## Prompt [LS4_1]

### Context
Three test failures were identified in [`reflection_LS3.md`](reflection_LS3.md) (Issues 1, 2, and 3):
1.  `test_process_hsi_line_invalid_quaternion` in [`test_vectorized_georef.py`](test_vectorized_georef.py:255-281) expects NaNs but `PoseTransformationError` is now correctly raised.
2.  `test_invalid_quaternion_handling_in_vectorized_function` in [`test_georeferencing.py`](test_georeferencing.py:624-643) is ineffective as it tests a local `ValueError` rather than the intended pipeline component behavior.
3.  `test_vectorized_vs_iterative_performance` in [`test_vectorized_georef.py`](test_vectorized_georef.py:287-332) has a flaky performance assertion.
These issues prevent the test suite from being fully reliable.

### Objective
Resolve all outstanding test failures from LS3 and ensure the test suite is robust, accurately reflecting code behavior and providing reliable feedback.

### Focus Areas
- Correcting assertions in `test_process_hsi_line_invalid_quaternion`.
- Refactoring or removing `test_invalid_quaternion_handling_in_vectorized_function`.
- Modifying `test_vectorized_vs_iterative_performance` to be a non-blocking benchmark.

### Code Reference
- [`test_vectorized_georef.py:255-281`](test_vectorized_georef.py:255-281) (Issue 1)
- [`test_georeferencing.py:624-643`](test_georeferencing.py:624-643) (Issue 2)
- [`test_vectorized_georef.py:287-332`](test_vectorized_georef.py:287-332) (Issue 3)

### Requirements
1.  Update `test_process_hsi_line_invalid_quaternion` ([`test_vectorized_georef.py:255-281`](test_vectorized_georef.py:255-281)) to assert that `PoseTransformationError` is raised, as per the recommendation in [`reflection_LS3.md`](reflection_LS3.md) (Issue 1).
2.  Refactor `test_invalid_quaternion_handling_in_vectorized_function` ([`test_georeferencing.py:624-643`](test_georeferencing.py:624-643)). If its intent was to test the fallback in `run_georeferencing` for `PoseTransformationError`, restructure it using appropriate mocks (e.g., mocking `process_hsi_line_vectorized` to raise the error and verifying fallback and logging). If its original intent is now covered by the fix for Issue 1 or other tests, remove it. (See [`reflection_LS3.md`](reflection_LS3.md), Issue 2 for detailed guidance).
3.  Modify `test_vectorized_vs_iterative_performance` ([`test_vectorized_georef.py:287-332`](test_vectorized_georef.py:287-332)) to log performance metrics instead of using a strict assertion that can cause flaky failures. The assertion `assert vectorized_time <= iterative_time * 2` should be removed or commented out, replaced by logging of `vectorized_time`, `iterative_time`, and `speedup`. (See [`reflection_LS3.md`](reflection_LS3.md), Issue 3).

### Expected Improvements
- All 85 tests (or the adjusted total after potential removal in Req 2) passing.
- Increased stability and reliability of the test suite.
- Correctness score ([`scores_LS3.json`](scores_LS3.json): 87.0) maintained or improved.

## Prompt [LS4_2]

### Context
The overall test coverage is 63.0% ([`scores_LS3.json`](scores_LS3.json)), which is significantly below the target of 80%. While LS3 improved coverage for specific files like [`georeference_hsi_pixels.py`](georeference_hsi_pixels.py) (estimated 68%) and [`vectorized_georef.py`](vectorized_georef.py) (77%), there are still gaps in the overall project coverage that need to be addressed to ensure robustness.

### Objective
Increase the overall test coverage of the HSI Georeferencing Pipeline to at least 75%, with a stretch goal of 80%, by adding targeted unit and integration tests to currently under-tested modules and functionalities.

### Focus Areas
- Identification of specific modules, functions, and code branches with low or no test coverage. This may require using a coverage analysis tool if available, or careful manual review of existing tests against the codebase.
- Core logic in [`georeference_hsi_pixels.py`](georeference_hsi_pixels.py), [`vectorized_georef.py`](vectorized_georef.py) that might still have untested paths despite recent improvements.
- Utility functions, e.g., in [`lever_arm_utils.py`](lever_arm_utils.py).
- Main pipeline orchestration logic in [`main_pipeline.py`](main_pipeline.py), including different configuration paths and error handling.
- Edge cases and error conditions across various components.

### Requirements
1.  Analyze the current test suite to identify specific functions, branches, or conditions that lack adequate test coverage.
2.  Write new unit tests for uncovered logical branches in key functions across the pipeline, particularly focusing on areas not extensively covered in LS3.
3.  Add integration tests for complex interactions between modules or data flows that are not yet fully exercised by existing tests (e.g., interactions between `main_pipeline.py` and the georeferencing core).
4.  Ensure all new tests are robust, cover meaningful scenarios (including edge cases and error handling), and follow the Arrange-Act-Assert pattern.
5.  Prioritize tests that cover critical functionality or areas prone to errors.

### Expected Improvements
- Overall test coverage score increased to >= 75% (aim for 80%).
- The `coverage` score in the subsequent `scores_LS4.json` reflects this improvement.
- Reduced risk of undetected bugs and regressions in future development.
- Increased confidence in the overall reliability of the pipeline.

## Prompt [LS4_3]

### Context
Issue 4 in [`reflection_LS3.md`](reflection_LS3.md) highlighted that the `calculate_ray_dsm_intersection` function in [`georeference_hsi_pixels.py`](georeference_hsi_pixels.py:170-302) remains lengthy (approx. 130 lines) and complex, despite improvements in LS3. The current complexity score is 74.0, below the target of 80 ([`scores_LS3.json`](scores_LS3.json)). Further refactoring by extracting the main ray marching loop can improve its clarity, maintainability, and testability.

### Objective
Reduce the complexity and improve the structure of `calculate_ray_dsm_intersection` by extracting its core ray marching and `brentq` logic into a well-defined private helper function.

### Focus Areas
- Decomposition of the main ray marching loop ([`georeference_hsi_pixels.py:224-301`](georeference_hsi_pixels.py:224-301)).
- Encapsulation of `brentq` usage and its associated error handling.
- Ensuring clear separation of concerns between the main function and the new helper.

### Code Reference
Function: `calculate_ray_dsm_intersection` in [`georeference_hsi_pixels.py:170-302`](georeference_hsi_pixels.py:170-302).
Conceptual refactoring example from [`reflection_LS3.md`](reflection_LS3.md) (Issue 4):
```python
# In georeference_hsi_pixels.py
#
# def _perform_ray_marching_and_brentq(P_sensor, d_world_normalized, func_to_solve, get_dsm_z_func, 
#                                    t_initial, diff_initial, max_dist, initial_step, tolerance_brentq):
#     # ... (contains the while t_search <= max_dist loop logic) ...
#     # ... (calls brentq internally) ...
#     # Returns (X_ground, Y_ground, Z_ground_coord) or (np.nan, np.nan, np.nan)
#
# def calculate_ray_dsm_intersection(P_sensor, d_world_normalized, interpolator, bounds, nodata_value, max_dist, initial_step, tolerance):
#     # ... (setup for get_dsm_z, func_to_solve, initial point check) ...
#     
#     # if initial point is valid:
#     #    return _perform_ray_marching_and_brentq(...)
#     # else:
#     #    return np.nan, np.nan, np.nan
```

### Requirements
1.  Refactor `calculate_ray_dsm_intersection` by extracting the main ray marching loop (approximately lines [`georeference_hsi_pixels.py:224-301`](georeference_hsi_pixels.py:224-301)) and the `brentq` solving logic into a new private helper function (e.g., `_find_intersection_point_with_dsm`).
2.  The new helper function should accept necessary parameters (like `P_sensor`, `d_world_normalized`, `func_to_solve`, `get_dsm_z`, `t_start`, `diff_start`, `max_dist`, `initial_step`, `tolerance_brentq`) and return the intersection coordinates or NaNs.
3.  The main `calculate_ray_dsm_intersection` function should then be simplified to handle initial setup (like defining `get_dsm_z`, `func_to_solve`, checking initial point validity) and then call the new helper function.
4.  Update and add comprehensive docstrings for both the refactored `calculate_ray_dsm_intersection` and the new helper function, clearly detailing their parameters, return values, logic, and any exceptions they might handle or propagate.
5.  Ensure all existing unit tests for `calculate_ray_dsm_intersection` (from [`test_georeferencing.py`](test_georeferencing.py)) continue to pass after the refactoring. If the new helper function is sufficiently complex, consider adding direct unit tests for it, though thorough testing of the parent function might suffice.

### Expected Improvements
- Reduced cyclomatic and cognitive complexity of `calculate_ray_dsm_intersection`.
- Improved `complexity` score in the subsequent `scores_LS4.json`.
- Enhanced readability, maintainability, and understandability of the DSM intersection code.
- Easier testing of the core intersection logic if the helper is tested independently.

## Prompt [LS4_4]

### Context
Minor code style issues and documentation opportunities were noted in [`reflection_LS3.md`](reflection_LS3.md). Specifically, Issue 5 pointed out local imports of custom exceptions in [`vectorized_georef.py`](vectorized_georef.py). Polishing these aspects improves overall code quality and maintainability.

### Objective
Address identified code style inconsistencies and enhance documentation clarity, particularly for modules modified in LS3 and LS4.

### Focus Areas
- Standardization of import statements in [`vectorized_georef.py`](vectorized_georef.py).
- Ensuring comprehensive and accurate docstrings for all public and complex private functions, especially those related to georeferencing logic.
- Reviewing and updating overall project documentation if impacted by LS4 changes.

### Code Reference
Local imports in [`vectorized_georef.py`](vectorized_georef.py) (Issue 5 from [`reflection_LS3.md`](reflection_LS3.md)):
- [`vectorized_georef.py:199`](vectorized_georef.py:199)
- [`vectorized_georef.py:206`](vectorized_georef.py:206)
- [`vectorized_georef.py:213`](vectorized_georef.py:213)

### Requirements
1.  Modify [`vectorized_georef.py`](vectorized_georef.py) to move the local imports of `PoseTransformationError` and `VectorizedProcessingError` from within `except` blocks to the top of the module, alongside other standard imports.
2.  Review and update docstrings for all functions modified or created as part of LS4 prompts (LS4_1, LS4_2, LS4_3), ensuring they are clear, accurate, and comprehensively describe parameters, return values, and key logic.
3.  Briefly review docstrings in other critical modules like [`main_pipeline.py`](main_pipeline.py) and [`lever_arm_utils.py`](lever_arm_utils.py) for completeness and clarity, making minor updates if obvious omissions or errors are found.
4.  If any changes in LS4 significantly alter pipeline behavior, configuration options, or outputs, update the main project documentation file ([`main_pipeline_documentation.md`](main_pipeline_documentation.md)) accordingly.

### Expected Improvements
- Improved code consistency and adherence to standard Python style guides.
- Enhanced code readability and maintainability due to clearer and more complete documentation.
- Easier onboarding for new developers or future maintainers of the codebase.