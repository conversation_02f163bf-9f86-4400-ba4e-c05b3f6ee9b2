"""
HSI Georeferencing Pipeline Package.

This package provides direct georeferencing capabilities for Hyperspectral Imaging (HSI) data
using pose information from WebODM and sensor models.

Main modules:
    - main_pipeline: Main pipeline orchestration
    - georeference_hsi_pixels: Core georeferencing algorithms
    - synchronize_hsi_webodm: HSI-WebODM data synchronization
    - create_consolidated_webodm_poses: WebODM pose consolidation
    - lever_arm_utils: Lever arm handling utilities
    - logging_config: Centralized logging configuration
    - pipeline_exceptions: Custom exception classes
    - vectorized_georef: Vectorized georeferencing functions
    - plotting: Visualization and plotting utilities

Author: HSI Georeferencing Pipeline Team
Version: 2.0 (LS7 Enhanced)
"""

__version__ = "2.0"
__author__ = "HSI Georeferencing Pipeline Team"

# Import main functions for easy access
from .main_pipeline import run_complete_pipeline, load_pipeline_config
from .georeference_hsi_pixels import run_georeferencing
from .synchronize_hsi_webodm import run_synchronization
from .create_consolidated_webodm_poses import run_consolidation
from .logging_config import setup_logging, get_logger
from .pipeline_exceptions import (
    PipelineError, PipelineConfigError, HSIDataError,
    SynchronizationError, GeoreferencingError, WebODMDataError,
    InputDataError, VectorizedProcessingError, PoseTransformationError
)

__all__ = [
    "run_complete_pipeline",
    "load_pipeline_config", 
    "run_georeferencing",
    "run_synchronization",
    "run_consolidation",
    "setup_logging",
    "get_logger",
    "PipelineError",
    "PipelineConfigError",
    "HSIDataError",
    "SynchronizationError", 
    "GeoreferencingError",
    "WebODMDataError",
    "InputDataError",
    "VectorizedProcessingError",
    "PoseTransformationError"
]
