["test_consolidation.py::TestConsolidationLS2Updates::test_function_signature_accepts_config_dict", "test_consolidation.py::TestConsolidationLS2Updates::test_invalid_json_raises_webodm_data_error", "test_consolidation.py::TestConsolidationLS2Updates::test_logging_integration", "test_consolidation.py::TestConsolidationLS2Updates::test_missing_config_key_raises_pipeline_config_error", "test_consolidation.py::TestConsolidationLS2Updates::test_missing_features_key_raises_webodm_data_error", "test_consolidation.py::TestConsolidationLS2Updates::test_missing_shots_file_raises_input_data_error", "test_consolidation.py::TestConsolidationLS2Updates::test_successful_consolidation_with_valid_data", "test_georeferencing.py::TestDSMHelperFunctions::test_create_ray_dsm_difference_function", "test_georeferencing.py::TestDSMHelperFunctions::test_find_dsm_entry_point_no_entry", "test_georeferencing.py::TestDSMHelperFunctions::test_find_dsm_entry_point_success", "test_georeferencing.py::TestDSMHelperFunctions::test_get_dsm_height_at_point_nodata_value", "test_georeferencing.py::TestDSMHelperFunctions::test_get_dsm_height_at_point_outside_bounds", "test_georeferencing.py::TestDSMHelperFunctions::test_get_dsm_height_at_point_valid", "test_georeferencing.py::TestDSMIntersection::test_ray_encounters_nodata_at_sensor_xy", "test_georeferencing.py::TestDSMIntersection::test_ray_exits_dsm_bounds_xy", "test_georeferencing.py::TestDSMIntersection::test_ray_marching_max_distance_exceeded", "test_georeferencing.py::TestDSMIntersection::test_ray_marching_with_sloped_surface", "test_georeferencing.py::TestDSMIntersection::test_ray_misses_dsm_points_away", "test_georeferencing.py::TestDSMIntersection::test_ray_starts_below_points_up", "test_georeferencing.py::TestDSMIntersection::test_successful_intersection_from_above", "test_georeferencing.py::TestGeoreferencingLS2Updates::test_function_signature_accepts_config_dict", "test_georeferencing.py::TestGeoreferencingLS2Updates::test_missing_config_key_raises_pipeline_config_error", "test_georeferencing.py::TestHelperFunctions::test_parse_hsi_header_missing_file", "test_georeferencing.py::TestHelperFunctions::test_parse_hsi_header_missing_lines", "test_georeferencing.py::TestHelperFunctions::test_parse_hsi_header_missing_samples", "test_georeferencing.py::TestHelperFunctions::test_parse_hsi_header_no_lever_arm", "test_georeferencing.py::TestHelperFunctions::test_parse_hsi_header_success", "test_georeferencing.py::TestHelperFunctions::test_parse_sensor_model_missing_file", "test_georeferencing.py::TestHelperFunctions::test_parse_sensor_model_success", "test_georeferencing.py::TestHelperFunctions::test_parse_sensor_model_too_few_entries", "test_georeferencing.py::TestHelperFunctions::test_parse_sensor_model_too_many_entries", "test_georeferencing.py::TestLS5IncreasedCoverage::test_parse_hsi_header_general_exception", "test_georeferencing.py::TestLS5IncreasedCoverage::test_parse_hsi_header_invalid_lever_arm_format", "test_georeferencing.py::TestLS5IncreasedCoverage::test_parse_sensor_model_fallback_to_two_column_format", "test_georeferencing.py::TestLS5IncreasedCoverage::test_parse_sensor_model_truncation_warning", "test_georeferencing.py::TestLS5IncreasedCoverage::test_run_georeferencing_dsm_file_not_found", "test_georeferencing.py::TestLS5IncreasedCoverage::test_run_georeferencing_missing_poses_file", "test_georeferencing.py::TestLS5IncreasedCoverage::test_run_georeferencing_pose_line_mismatch", "test_georeferencing.py::TestLS5LeverArmParsing::test_parse_hsi_header_lever_arm_both_keys_different_values", "test_georeferencing.py::TestLS5LeverArmParsing::test_parse_hsi_header_lever_arm_both_keys_same_values", "test_georeferencing.py::TestLS5LeverArmParsing::test_parse_hsi_header_lever_arm_key_only", "test_georeferencing.py::TestLS5LeverArmParsing::test_parse_hsi_header_lever_arm_offset_only", "test_georeferencing.py::TestLS5PerformanceOptimizations::test_adaptive_step_sizing_behavior", "test_georeferencing.py::TestLS5PerformanceOptimizations::test_bounds_checking_optimization", "test_georeferencing.py::TestLS5PerformanceOptimizations::test_optimized_ray_calculation_with_unpacked_components", "test_georeferencing.py::TestLS5PerformanceOptimizations::test_performance_with_multiple_rays", "test_georeferencing.py::TestLS5SensorModelLogging::test_parse_sensor_model_angles_in_degrees_with_enhanced_warning", "test_georeferencing.py::TestLS5SensorModelLogging::test_parse_sensor_model_angles_in_radians_no_warning", "test_georeferencing.py::TestLS5SensorModelLogging::test_parse_sensor_model_mixed_angles_trigger_degrees", "test_georeferencing.py::TestLS5SimplifiedBrentqHandling::test_brentq_handling_no_sign_change", "test_georeferencing.py::TestLS5SimplifiedBrentqHandling::test_brentq_handling_with_nan_endpoints", "test_georeferencing.py::TestLS5SimplifiedBrentqHandling::test_brentq_handling_with_valid_bracket", "test_georeferencing.py::TestLS6DSMPathResolution::test_dsm_path_resolution_absolute_path_unchanged", "test_georeferencing.py::TestLS6DSMPathResolution::test_dsm_path_resolution_relative_to_config", "test_georeferencing.py::TestLS6VectorizedPathFix::test_run_georeferencing_flat_plane_vectorized_invocation", "test_georeferencing.py::TestLS6VectorizedPathFix::test_run_georeferencing_flat_plane_vectorized_result_processing", "test_georeferencing.py::TestLS7InvalidZGroundMethod::test_run_georeferencing_invalid_z_ground_method_error_message", "test_georeferencing.py::TestLS7InvalidZGroundMethod::test_run_georeferencing_invalid_z_ground_method_raises_error", "test_georeferencing.py::TestLoggingIntegration::test_logging_integration", "test_georeferencing.py::TestSensorModelParsing::test_parse_sensor_model_angles_in_degrees", "test_georeferencing.py::TestSensorModelParsing::test_parse_sensor_model_angles_in_radians", "test_georeferencing.py::TestVectorizationIntegration::test_vectorized_processing_called_for_flat_plane", "test_georeferencing.py::TestVectorizedExceptionHandling::test_invalid_quaternion_handling_in_vectorized_function", "test_georeferencing.py::TestVectorizedExceptionHandling::test_run_georeferencing_fallback_on_pose_transformation_error", "test_georeferencing.py::TestVectorizedExceptionHandling::test_vectorized_processing_specific_exception_fallback", "test_lever_arm.py::TestIntegrationLeverArm::test_lever_arm_integration_scenario", "test_lever_arm.py::TestIntegrationLeverArm::test_lever_arm_precision", "test_lever_arm.py::TestLeverArmDetermination::test_dead_code_coverage_line_75", "test_lever_arm.py::TestLeverArmDetermination::test_lever_arm_both_zero", "test_lever_arm.py::TestLeverArmDetermination::test_lever_arm_config_override", "test_lever_arm.py::TestLeverArmDetermination::test_lever_arm_hdr_only", "test_lever_arm.py::TestLeverArmDetermination::test_lever_arm_hdr_with_zero_config", "test_lever_arm.py::TestLeverArmDetermination::test_lever_arm_none_inputs", "test_lever_arm.py::TestLeverArmDetermination::test_warning_zero_effective_arm_with_nonzero_hdr", "test_lever_arm.py::TestLeverArmValidation::test_invalid_lever_arm_shape", "test_lever_arm.py::TestLeverArmValidation::test_invalid_lever_arm_values", "test_lever_arm.py::TestLeverArmValidation::test_large_lever_arm_warning", "test_lever_arm.py::TestLeverArmValidation::test_none_lever_arm", "test_lever_arm.py::TestLeverArmValidation::test_valid_lever_arm", "test_logging_config.py::TestGetLogger::test_get_logger_different_names", "test_logging_config.py::TestGetLogger::test_get_logger_retrieval", "test_logging_config.py::TestGetLogger::test_get_logger_same_name_returns_same_instance", "test_logging_config.py::TestLoggingIntegration::test_logging_configuration_persistence", "test_logging_config.py::TestLoggingIntegration::test_logging_handlers_configuration", "test_logging_config.py::TestSetupLogging::test_setup_logging_applies_config", "test_logging_config.py::TestSetupLogging::test_setup_logging_creates_log_directory", "test_logging_config.py::TestSetupLogging::test_setup_logging_different_log_levels", "test_logging_config.py::TestSetupLogging::test_setup_logging_invalid_level_defaults_to_info", "test_main_pipeline.py::TestAdditionalErrorCoverage::test_consolidation_exception_coverage", "test_main_pipeline.py::TestAdditionalErrorCoverage::test_consolidation_failure_coverage", "test_main_pipeline.py::TestAdditionalErrorCoverage::test_georeferencing_exception_coverage", "test_main_pipeline.py::TestAdditionalErrorCoverage::test_georeferencing_failure_coverage", "test_main_pipeline.py::TestAdditionalErrorCoverage::test_plotting_exception_coverage", "test_main_pipeline.py::TestAdditionalErrorCoverage::test_rgb_creation_exception_coverage", "test_main_pipeline.py::TestAdditionalErrorCoverage::test_unexpected_exception_coverage", "test_main_pipeline.py::TestConfigurationLoading::test_load_config_with_unicode", "test_main_pipeline.py::TestConfigurationLoading::test_load_invalid_toml_syntax", "test_main_pipeline.py::TestConfigurationLoading::test_load_nonexistent_config", "test_main_pipeline.py::TestConfigurationLoading::test_load_valid_config", "test_main_pipeline.py::TestConfigurationPassing::test_config_object_immutability", "test_main_pipeline.py::TestConfigurationPassing::test_config_passed_to_submodules", "test_main_pipeline.py::TestLoggingIntegration::test_logging_messages", "test_main_pipeline.py::TestLoggingIntegration::test_logging_setup", "test_main_pipeline.py::TestPipelineOrchestration::test_pipeline_config_error", "test_main_pipeline.py::TestPipelineOrchestration::test_pipeline_failure_in_critical_step", "test_main_pipeline.py::TestPipelineOrchestration::test_pipeline_unexpected_error", "test_main_pipeline.py::TestPipelineOrchestration::test_pipeline_warning_in_optional_step", "test_main_pipeline.py::TestPipelineOrchestration::test_successful_pipeline_execution", "test_synchronization.py::TestHelperFunctions::test_convert_hsi_timestamp_to_ns", "test_synchronization.py::TestHelperFunctions::test_interpolate_pose_exact_match", "test_synchronization.py::TestHelperFunctions::test_interpolate_pose_extrapolation_after", "test_synchronization.py::TestHelperFunctions::test_interpolate_pose_extrapolation_before", "test_synchronization.py::TestHelperFunctions::test_interpolate_pose_interpolation", "test_synchronization.py::TestHelperFunctions::test_load_hsi_data_invalid_format", "test_synchronization.py::TestHelperFunctions::test_load_hsi_data_success", "test_synchronization.py::TestHelperFunctions::test_load_webodm_data_missing_columns", "test_synchronization.py::TestHelperFunctions::test_load_webodm_data_success", "test_synchronization.py::TestHelperFunctions::test_parse_hdr_file_missing_file", "test_synchronization.py::TestHelperFunctions::test_parse_hdr_file_success", "test_synchronization.py::TestLoggingIntegration::test_logging_integration", "test_synchronization.py::TestSynchronizationLS2Updates::test_function_signature_accepts_config_dict", "test_synchronization.py::TestSynchronizationLS2Updates::test_missing_config_key_raises_pipeline_config_error", "test_synchronize_hsi_webodm.py::TestConvertHsiTimestamp::test_convert_hsi_timestamp_to_ns_basic", "test_synchronize_hsi_webodm.py::TestConvertHsiTimestamp::test_convert_hsi_timestamp_to_ns_large_value", "test_synchronize_hsi_webodm.py::TestConvertHsiTimestamp::test_convert_hsi_timestamp_to_ns_zero", "test_synchronize_hsi_webodm.py::TestInterpolatePose::test_interpolate_pose_before_all_timestamps", "test_synchronize_hsi_webodm.py::TestInterpolatePose::test_interpolate_pose_empty_poses", "test_synchronize_hsi_webodm.py::TestInterpolatePose::test_interpolate_pose_exact_match", "test_synchronize_hsi_webodm.py::TestParseHdrFile::test_parse_hdr_file_missing_lines", "test_synchronize_hsi_webodm.py::TestParseHdrFile::test_parse_hdr_file_not_found", "test_synchronize_hsi_webodm.py::TestParseHdrFile::test_parse_hdr_file_valid_content", "test_synchronize_hsi_webodm.py::TestRunSynchronization::test_run_synchronization_basic_functionality", "test_synchronize_hsi_webodm.py::TestRunSynchronization::test_run_synchronization_file_not_found", "test_synchronize_hsi_webodm.py::TestRunSynchronization::test_run_synchronization_missing_config_keys", "test_synchronize_hsi_webodm.py::TestRunSynchronization::test_run_synchronization_with_time_threshold", "test_vectorized_georef.py::TestCalculateSensorViewVectorsVectorized::test_calculate_sensor_view_vectors_valid_inputs", "test_vectorized_georef.py::TestLS6VectorizedGeoreferencingCoverage::test_calculate_sensor_pixel_vectors_vectorized_boresight_center", "test_vectorized_georef.py::TestLS6VectorizedGeoreferencingCoverage::test_intersect_rays_with_horizontal_plane_vectorized_nadir_pointing", "test_vectorized_georef.py::TestLS6VectorizedGeoreferencingCoverage::test_intersect_rays_with_horizontal_plane_vectorized_ray_parallel_to_plane", "test_vectorized_georef.py::TestLS6VectorizedGeoreferencingCoverage::test_prepare_rotation_matrices_vectorized_90deg_x_rotation", "test_vectorized_georef.py::TestLS6VectorizedGeoreferencingCoverage::test_prepare_rotation_matrices_vectorized_identity", "test_vectorized_georef.py::TestLS6VectorizedGeoreferencingCoverage::test_process_hsi_line_vectorized_dsm_method_missing_interpolator", "test_vectorized_georef.py::TestLS6VectorizedGeoreferencingCoverage::test_process_hsi_line_vectorized_flat_plane_nadir_simple", "test_vectorized_georef.py::TestLS6VectorizedGeoreferencingCoverage::test_transform_vectors_to_world_vectorized_identity_rotations", "test_vectorized_georef.py::TestPerformanceBenchmark::test_log_vectorized_vs_iterative_performance", "test_vectorized_georef.py::TestPerformanceBenchmark::test_vectorized_vs_iterative_performance", "test_vectorized_georef.py::TestProcessHSILineVectorizedDSM::test_process_hsi_line_vectorized_dsm_correctness", "test_vectorized_georef.py::TestVectorizedFlatPlaneIntersection::test_flat_plane_intersection_backward_rays", "test_vectorized_georef.py::TestVectorizedFlatPlaneIntersection::test_flat_plane_intersection_no_intersection", "test_vectorized_georef.py::TestVectorizedFlatPlaneIntersection::test_flat_plane_intersection_simple", "test_vectorized_georef.py::TestVectorizedFlatPlaneIntersection::test_flat_plane_intersection_threshold", "test_vectorized_georef.py::TestVectorizedLineProcessing::test_process_hsi_line_flat_plane", "test_vectorized_georef.py::TestVectorizedLineProcessing::test_process_hsi_line_invalid_quaternion", "test_vectorized_georef.py::TestVectorizedSensorViewVectors::test_sensor_view_vectors_multiple_pixels", "test_vectorized_georef.py::TestVectorizedSensorViewVectors::test_sensor_view_vectors_single_pixel", "test_vectorized_georef.py::TestVectorizedSensorViewVectors::test_sensor_view_vectors_with_correction", "test_vectorized_georef.py::TestVectorizedSensorViewVectors::test_sensor_view_vectors_zero_norm_handling", "test_vectorized_georef.py::TestVectorizedWorldTransform::test_world_transform_identity", "test_vectorized_georef.py::TestVectorizedWorldTransform::test_world_transform_multiple_vectors", "test_vectorized_georef.py::TestVectorizedWorldTransform::test_world_transform_rotation", "tests/test_consolidation.py::TestConsolidationLS2Updates::test_function_signature_accepts_config_dict", "tests/test_consolidation.py::TestConsolidationLS2Updates::test_invalid_json_raises_webodm_data_error", "tests/test_consolidation.py::TestConsolidationLS2Updates::test_logging_integration", "tests/test_consolidation.py::TestConsolidationLS2Updates::test_missing_config_key_raises_pipeline_config_error", "tests/test_consolidation.py::TestConsolidationLS2Updates::test_missing_features_key_raises_webodm_data_error", "tests/test_consolidation.py::TestConsolidationLS2Updates::test_missing_shots_file_raises_input_data_error", "tests/test_consolidation.py::TestConsolidationLS2Updates::test_successful_consolidation_with_valid_data", "tests/test_georeferencing.py::TestDSMHelperFunctions::test_create_ray_dsm_difference_function", "tests/test_georeferencing.py::TestDSMHelperFunctions::test_find_dsm_entry_point_no_entry", "tests/test_georeferencing.py::TestDSMHelperFunctions::test_find_dsm_entry_point_success", "tests/test_georeferencing.py::TestDSMHelperFunctions::test_get_dsm_height_at_point_nodata_value", "tests/test_georeferencing.py::TestDSMHelperFunctions::test_get_dsm_height_at_point_outside_bounds", "tests/test_georeferencing.py::TestDSMHelperFunctions::test_get_dsm_height_at_point_valid", "tests/test_georeferencing.py::TestDSMIntersection::test_ray_encounters_nodata_at_sensor_xy", "tests/test_georeferencing.py::TestDSMIntersection::test_ray_exits_dsm_bounds_xy", "tests/test_georeferencing.py::TestDSMIntersection::test_ray_marching_max_distance_exceeded", "tests/test_georeferencing.py::TestDSMIntersection::test_ray_marching_with_sloped_surface", "tests/test_georeferencing.py::TestDSMIntersection::test_ray_misses_dsm_points_away", "tests/test_georeferencing.py::TestDSMIntersection::test_ray_starts_below_points_up", "tests/test_georeferencing.py::TestDSMIntersection::test_successful_intersection_from_above", "tests/test_georeferencing.py::TestGeoreferencingLS2Updates::test_function_signature_accepts_config_dict", "tests/test_georeferencing.py::TestGeoreferencingLS2Updates::test_missing_config_key_raises_pipeline_config_error", "tests/test_georeferencing.py::TestHelperFunctions::test_parse_hsi_header_missing_file", "tests/test_georeferencing.py::TestHelperFunctions::test_parse_hsi_header_missing_lines", "tests/test_georeferencing.py::TestHelperFunctions::test_parse_hsi_header_missing_samples", "tests/test_georeferencing.py::TestHelperFunctions::test_parse_hsi_header_no_lever_arm", "tests/test_georeferencing.py::TestHelperFunctions::test_parse_hsi_header_success", "tests/test_georeferencing.py::TestHelperFunctions::test_parse_sensor_model_missing_file", "tests/test_georeferencing.py::TestHelperFunctions::test_parse_sensor_model_success", "tests/test_georeferencing.py::TestHelperFunctions::test_parse_sensor_model_too_few_entries", "tests/test_georeferencing.py::TestHelperFunctions::test_parse_sensor_model_too_many_entries", "tests/test_georeferencing.py::TestLS5IncreasedCoverage::test_parse_hsi_header_general_exception", "tests/test_georeferencing.py::TestLS5IncreasedCoverage::test_parse_hsi_header_invalid_lever_arm_format", "tests/test_georeferencing.py::TestLS5IncreasedCoverage::test_parse_sensor_model_fallback_to_two_column_format", "tests/test_georeferencing.py::TestLS5IncreasedCoverage::test_parse_sensor_model_truncation_warning", "tests/test_georeferencing.py::TestLS5IncreasedCoverage::test_run_georeferencing_dsm_file_not_found", "tests/test_georeferencing.py::TestLS5IncreasedCoverage::test_run_georeferencing_missing_poses_file", "tests/test_georeferencing.py::TestLS5IncreasedCoverage::test_run_georeferencing_pose_line_mismatch", "tests/test_georeferencing.py::TestLS5LeverArmParsing::test_parse_hsi_header_lever_arm_both_keys_different_values", "tests/test_georeferencing.py::TestLS5LeverArmParsing::test_parse_hsi_header_lever_arm_both_keys_same_values", "tests/test_georeferencing.py::TestLS5LeverArmParsing::test_parse_hsi_header_lever_arm_key_only", "tests/test_georeferencing.py::TestLS5LeverArmParsing::test_parse_hsi_header_lever_arm_offset_only", "tests/test_georeferencing.py::TestLS5PerformanceOptimizations::test_adaptive_step_sizing_behavior", "tests/test_georeferencing.py::TestLS5PerformanceOptimizations::test_bounds_checking_optimization", "tests/test_georeferencing.py::TestLS5PerformanceOptimizations::test_optimized_ray_calculation_with_unpacked_components", "tests/test_georeferencing.py::TestLS5PerformanceOptimizations::test_performance_with_multiple_rays", "tests/test_georeferencing.py::TestLS5SensorModelLogging::test_parse_sensor_model_angles_in_degrees_with_enhanced_warning", "tests/test_georeferencing.py::TestLS5SensorModelLogging::test_parse_sensor_model_angles_in_radians_no_warning", "tests/test_georeferencing.py::TestLS5SensorModelLogging::test_parse_sensor_model_mixed_angles_trigger_degrees", "tests/test_georeferencing.py::TestLS5SimplifiedBrentqHandling::test_brentq_handling_no_sign_change", "tests/test_georeferencing.py::TestLS5SimplifiedBrentqHandling::test_brentq_handling_with_nan_endpoints", "tests/test_georeferencing.py::TestLS5SimplifiedBrentqHandling::test_brentq_handling_with_valid_bracket", "tests/test_georeferencing.py::TestLS6DSMPathResolution::test_dsm_path_resolution_absolute_path_unchanged", "tests/test_georeferencing.py::TestLS6DSMPathResolution::test_dsm_path_resolution_relative_file_not_found_at_resolved_path", "tests/test_georeferencing.py::TestLS6DSMPathResolution::test_dsm_path_resolution_relative_to_config", "tests/test_georeferencing.py::TestLS6VectorizedPathFix::test_run_georeferencing_flat_plane_vectorized_invocation", "tests/test_georeferencing.py::TestLS6VectorizedPathFix::test_run_georeferencing_flat_plane_vectorized_result_processing", "tests/test_georeferencing.py::TestLS7InvalidZGroundMethod::test_run_georeferencing_invalid_z_ground_method_error_message", "tests/test_georeferencing.py::TestLS7InvalidZGroundMethod::test_run_georeferencing_invalid_z_ground_method_raises_error", "tests/test_georeferencing.py::TestLoggingIntegration::test_logging_integration", "tests/test_georeferencing.py::TestSensorModelParsing::test_parse_sensor_model_angles_in_degrees", "tests/test_georeferencing.py::TestSensorModelParsing::test_parse_sensor_model_angles_in_radians", "tests/test_georeferencing.py::TestVectorizationIntegration::test_vectorized_processing_called_for_flat_plane", "tests/test_georeferencing.py::TestVectorizedExceptionHandling::test_run_georeferencing_fallback_on_pose_transformation_error", "tests/test_georeferencing.py::TestVectorizedExceptionHandling::test_vectorized_processing_specific_exception_fallback", "tests/test_lever_arm.py::TestIntegrationLeverArm::test_lever_arm_integration_scenario", "tests/test_lever_arm.py::TestIntegrationLeverArm::test_lever_arm_precision", "tests/test_lever_arm.py::TestLeverArmDetermination::test_dead_code_coverage_line_75", "tests/test_lever_arm.py::TestLeverArmDetermination::test_lever_arm_both_zero", "tests/test_lever_arm.py::TestLeverArmDetermination::test_lever_arm_config_override", "tests/test_lever_arm.py::TestLeverArmDetermination::test_lever_arm_hdr_only", "tests/test_lever_arm.py::TestLeverArmDetermination::test_lever_arm_hdr_with_zero_config", "tests/test_lever_arm.py::TestLeverArmDetermination::test_lever_arm_none_inputs", "tests/test_lever_arm.py::TestLeverArmDetermination::test_warning_zero_effective_arm_with_nonzero_hdr", "tests/test_lever_arm.py::TestLeverArmValidation::test_invalid_lever_arm_shape", "tests/test_lever_arm.py::TestLeverArmValidation::test_invalid_lever_arm_values", "tests/test_lever_arm.py::TestLeverArmValidation::test_large_lever_arm_warning", "tests/test_lever_arm.py::TestLeverArmValidation::test_none_lever_arm", "tests/test_lever_arm.py::TestLeverArmValidation::test_valid_lever_arm", "tests/test_logging_config.py::TestGetLogger::test_get_logger_different_names", "tests/test_logging_config.py::TestGetLogger::test_get_logger_retrieval", "tests/test_logging_config.py::TestGetLogger::test_get_logger_same_name_returns_same_instance", "tests/test_logging_config.py::TestLoggingIntegration::test_logging_configuration_persistence", "tests/test_logging_config.py::TestLoggingIntegration::test_logging_handlers_configuration", "tests/test_logging_config.py::TestSetupLogging::test_setup_logging_applies_config", "tests/test_logging_config.py::TestSetupLogging::test_setup_logging_creates_log_directory", "tests/test_logging_config.py::TestSetupLogging::test_setup_logging_different_log_levels", "tests/test_logging_config.py::TestSetupLogging::test_setup_logging_invalid_level_defaults_to_info", "tests/test_main_pipeline.py::TestAdditionalErrorCoverage::test_consolidation_exception_coverage", "tests/test_main_pipeline.py::TestAdditionalErrorCoverage::test_consolidation_failure_coverage", "tests/test_main_pipeline.py::TestAdditionalErrorCoverage::test_georeferencing_exception_coverage", "tests/test_main_pipeline.py::TestAdditionalErrorCoverage::test_georeferencing_failure_coverage", "tests/test_main_pipeline.py::TestAdditionalErrorCoverage::test_plotting_exception_coverage", "tests/test_main_pipeline.py::TestAdditionalErrorCoverage::test_rgb_creation_exception_coverage", "tests/test_main_pipeline.py::TestAdditionalErrorCoverage::test_unexpected_exception_coverage", "tests/test_main_pipeline.py::TestConfigurationLoading::test_load_config_with_unicode", "tests/test_main_pipeline.py::TestConfigurationLoading::test_load_invalid_toml_syntax", "tests/test_main_pipeline.py::TestConfigurationLoading::test_load_nonexistent_config", "tests/test_main_pipeline.py::TestConfigurationLoading::test_load_valid_config", "tests/test_main_pipeline.py::TestConfigurationPassing::test_config_object_immutability", "tests/test_main_pipeline.py::TestConfigurationPassing::test_config_passed_to_submodules", "tests/test_main_pipeline.py::TestLS7ModuleLevelLogger::test_load_pipeline_config_uses_module_logger", "tests/test_main_pipeline.py::TestLS7ModuleLevelLogger::test_logging_messages", "tests/test_main_pipeline.py::TestLS7ModuleLevelLogger::test_module_logger_initialization_pattern", "tests/test_main_pipeline.py::TestLS7ModuleLevelLogger::test_run_complete_pipeline_uses_module_logger_and_sets_up_logging", "tests/test_main_pipeline.py::TestLoggingIntegration::test_logging_setup", "tests/test_main_pipeline.py::TestPipelineOrchestration::test_pipeline_config_error", "tests/test_main_pipeline.py::TestPipelineOrchestration::test_pipeline_failure_in_critical_step", "tests/test_main_pipeline.py::TestPipelineOrchestration::test_pipeline_unexpected_error", "tests/test_main_pipeline.py::TestPipelineOrchestration::test_pipeline_warning_in_optional_step", "tests/test_main_pipeline.py::TestPipelineOrchestration::test_successful_pipeline_execution", "tests/test_synchronization.py::TestHelperFunctions::test_convert_hsi_timestamp_to_ns", "tests/test_synchronization.py::TestHelperFunctions::test_interpolate_pose_exact_match", "tests/test_synchronization.py::TestHelperFunctions::test_interpolate_pose_extrapolation_after", "tests/test_synchronization.py::TestHelperFunctions::test_interpolate_pose_extrapolation_before", "tests/test_synchronization.py::TestHelperFunctions::test_interpolate_pose_interpolation", "tests/test_synchronization.py::TestHelperFunctions::test_load_hsi_data_invalid_format", "tests/test_synchronization.py::TestHelperFunctions::test_load_hsi_data_success", "tests/test_synchronization.py::TestHelperFunctions::test_load_webodm_data_missing_columns", "tests/test_synchronization.py::TestHelperFunctions::test_load_webodm_data_success", "tests/test_synchronization.py::TestHelperFunctions::test_parse_hdr_file_missing_file", "tests/test_synchronization.py::TestHelperFunctions::test_parse_hdr_file_success", "tests/test_synchronization.py::TestLoggingIntegration::test_logging_integration", "tests/test_synchronization.py::TestSynchronizationLS2Updates::test_function_signature_accepts_config_dict", "tests/test_synchronization.py::TestSynchronizationLS2Updates::test_missing_config_key_raises_pipeline_config_error", "tests/test_synchronize_hsi_webodm.py::TestConvertHsiTimestamp::test_convert_hsi_timestamp_to_ns_basic", "tests/test_synchronize_hsi_webodm.py::TestConvertHsiTimestamp::test_convert_hsi_timestamp_to_ns_large_value", "tests/test_synchronize_hsi_webodm.py::TestConvertHsiTimestamp::test_convert_hsi_timestamp_to_ns_zero", "tests/test_synchronize_hsi_webodm.py::TestInterpolatePose::test_interpolate_pose_before_all_timestamps", "tests/test_synchronize_hsi_webodm.py::TestInterpolatePose::test_interpolate_pose_empty_poses", "tests/test_synchronize_hsi_webodm.py::TestInterpolatePose::test_interpolate_pose_exact_match", "tests/test_synchronize_hsi_webodm.py::TestParseHdrFile::test_parse_hdr_file_missing_lines", "tests/test_synchronize_hsi_webodm.py::TestParseHdrFile::test_parse_hdr_file_not_found", "tests/test_synchronize_hsi_webodm.py::TestParseHdrFile::test_parse_hdr_file_valid_content", "tests/test_synchronize_hsi_webodm.py::TestRunSynchronization::test_run_synchronization_basic_functionality", "tests/test_synchronize_hsi_webodm.py::TestRunSynchronization::test_run_synchronization_file_not_found", "tests/test_synchronize_hsi_webodm.py::TestRunSynchronization::test_run_synchronization_missing_config_keys", "tests/test_synchronize_hsi_webodm.py::TestRunSynchronization::test_run_synchronization_with_time_threshold", "tests/test_vectorized_georef.py::TestCalculateFlatPlaneIntersectionsVectorized::test_calculate_flat_plane_intersections_custom_threshold", "tests/test_vectorized_georef.py::TestCalculateFlatPlaneIntersectionsVectorized::test_calculate_flat_plane_intersections_nadir_pointing", "tests/test_vectorized_georef.py::TestCalculateFlatPlaneIntersectionsVectorized::test_calculate_flat_plane_intersections_oblique_rays", "tests/test_vectorized_georef.py::TestCalculateFlatPlaneIntersectionsVectorized::test_calculate_flat_plane_intersections_parallel_ray", "tests/test_vectorized_georef.py::TestCalculateFlatPlaneIntersectionsVectorized::test_calculate_flat_plane_intersections_ray_pointing_away", "tests/test_vectorized_georef.py::TestCalculateSensorViewVectorsVectorized::test_calculate_sensor_view_vectors_single_pixel", "tests/test_vectorized_georef.py::TestCalculateSensorViewVectorsVectorized::test_calculate_sensor_view_vectors_valid_inputs", "tests/test_vectorized_georef.py::TestCalculateSensorViewVectorsVectorized::test_calculate_sensor_view_vectors_with_scale_and_offset", "tests/test_vectorized_georef.py::TestCalculateSensorViewVectorsVectorized::test_calculate_sensor_view_vectors_zero_norm_handling", "tests/test_vectorized_georef.py::TestLS6VectorizedGeoreferencingCoverage::test_calculate_sensor_pixel_vectors_vectorized_boresight_center", "tests/test_vectorized_georef.py::TestLS6VectorizedGeoreferencingCoverage::test_intersect_rays_with_horizontal_plane_vectorized_nadir_pointing", "tests/test_vectorized_georef.py::TestLS6VectorizedGeoreferencingCoverage::test_intersect_rays_with_horizontal_plane_vectorized_ray_parallel_to_plane", "tests/test_vectorized_georef.py::TestLS6VectorizedGeoreferencingCoverage::test_prepare_rotation_matrices_vectorized_90deg_x_rotation", "tests/test_vectorized_georef.py::TestLS6VectorizedGeoreferencingCoverage::test_prepare_rotation_matrices_vectorized_identity", "tests/test_vectorized_georef.py::TestLS6VectorizedGeoreferencingCoverage::test_process_hsi_line_vectorized_dsm_method_missing_interpolator", "tests/test_vectorized_georef.py::TestLS6VectorizedGeoreferencingCoverage::test_process_hsi_line_vectorized_flat_plane_nadir_simple", "tests/test_vectorized_georef.py::TestLS6VectorizedGeoreferencingCoverage::test_transform_vectors_to_world_vectorized_identity_rotations", "tests/test_vectorized_georef.py::TestPerformanceBenchmark::test_log_vectorized_vs_iterative_performance", "tests/test_vectorized_georef.py::TestProcessHSILineVectorizedDSM::test_process_hsi_line_vectorized_dsm_correctness", "tests/test_vectorized_georef.py::TestTransformToWorldCoordinatesVectorized::test_transform_to_world_coordinates_identity_rotations", "tests/test_vectorized_georef.py::TestTransformToWorldCoordinatesVectorized::test_transform_to_world_coordinates_known_rotation", "tests/test_vectorized_georef.py::TestTransformToWorldCoordinatesVectorized::test_transform_to_world_coordinates_multiple_vectors", "tests/test_vectorized_georef.py::TestVectorizedFlatPlaneIntersection::test_flat_plane_intersection_backward_rays", "tests/test_vectorized_georef.py::TestVectorizedFlatPlaneIntersection::test_flat_plane_intersection_no_intersection", "tests/test_vectorized_georef.py::TestVectorizedFlatPlaneIntersection::test_flat_plane_intersection_simple", "tests/test_vectorized_georef.py::TestVectorizedFlatPlaneIntersection::test_flat_plane_intersection_threshold", "tests/test_vectorized_georef.py::TestVectorizedLineProcessing::test_process_hsi_line_flat_plane", "tests/test_vectorized_georef.py::TestVectorizedLineProcessing::test_process_hsi_line_invalid_quaternion", "tests/test_vectorized_georef.py::TestVectorizedSensorViewVectors::test_sensor_view_vectors_multiple_pixels", "tests/test_vectorized_georef.py::TestVectorizedSensorViewVectors::test_sensor_view_vectors_single_pixel", "tests/test_vectorized_georef.py::TestVectorizedSensorViewVectors::test_sensor_view_vectors_with_correction", "tests/test_vectorized_georef.py::TestVectorizedSensorViewVectors::test_sensor_view_vectors_zero_norm_handling", "tests/test_vectorized_georef.py::TestVectorizedWorldTransform::test_world_transform_identity", "tests/test_vectorized_georef.py::TestVectorizedWorldTransform::test_world_transform_multiple_vectors", "tests/test_vectorized_georef.py::TestVectorizedWorldTransform::test_world_transform_rotation"]