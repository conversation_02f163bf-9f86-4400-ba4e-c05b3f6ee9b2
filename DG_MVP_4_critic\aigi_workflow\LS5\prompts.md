# Prompts for Layer LS5: HSI Georeferencing Pipeline

## Overall Context for LS5

Based on the analysis of [`scores_LS4.json`](scores_LS4.json) and [`reflection_LS4.md`](reflection_LS4.md):
- **Progress**: LS4 made good progress, with an overall score of 75.6 (delta +3.6), meeting the overall quality target. Complexity (81.0) and Correctness (92.0) scores are strong.
- **Key Areas for Improvement**:
    - **Test Coverage**: Currently at 65.0 (reported line coverage 57%, branch coverage 52%), significantly below the 80% target. This is a primary focus for LS5.
    - **Performance**: Score is 63.0, below the 75 target. DSM intersection is a key area for optimization.
    - **Outstanding Issues**: Several "Top Issues" (mostly style and minor logic/robustness) from [`reflection_LS4.md`](reflection_LS4.md) need to be addressed.
- **LS5 Goals**:
    1. Resolve all outstanding "Top Issues" and "Style Recommendations" from [`reflection_LS4.md`](reflection_LS4.md).
    2. Strategically improve test coverage towards the 80% target, focusing on under-tested modules like [`georeference_hsi_pixels.py`](georeference_hsi_pixels.py) and [`vectorized_georef.py`](vectorized_georef.py), and enhancing branch coverage.
    3. Implement performance enhancements, particularly for DSM intersection calculations.

---

## Prompt [LS5_1]

### Context
Based on [`reflection_LS4.md`](reflection_LS4.md), several outstanding issues related to code style and minor logical improvements need to be addressed. These are important for long-term maintainability and robustness. LS4 achieved an overall score of 75.6, but specific areas like test coverage (65.0) and performance (63.0) are still below targets. Addressing these foundational issues first will support further improvements.

### Objective
Resolve specific code style issues and minor bugs identified in [`reflection_LS4.md`](reflection_LS4.md) (Issues 1, 2, 4, 5) to improve overall code quality, readability, and maintainability.

### Focus Areas
- Issue 1: Local Imports of `PoseTransformationError` and `VectorizedProcessingError` in [`vectorized_georef.py`](vectorized_georef.py).
- Issue 2: Missing Module Docstring at the top of [`main_pipeline.py`](main_pipeline.py).
- Issue 4: Ambiguity in `parse_hsi_header` lever arm parsing order within [`georeference_hsi_pixels.py`](georeference_hsi_pixels.py).
- Issue 5: Potential Edge Case in `parse_sensor_model` angle interpretation logging within [`georeference_hsi_pixels.py`](georeference_hsi_pixels.py).

### Code Reference
- [`vectorized_georef.py`](vectorized_georef.py) (for local imports)
- [`main_pipeline.py`](main_pipeline.py) (for module docstring)
- [`georeference_hsi_pixels.py:79-90`](georeference_hsi_pixels.py:79-90) (for `parse_hsi_header`)
- [`georeference_hsi_pixels.py:151-166`](georeference_hsi_pixels.py:151-166) (for `parse_sensor_model`)

### Requirements
1.  In [`vectorized_georef.py`](vectorized_georef.py), move the imports for `PoseTransformationError` and `VectorizedProcessingError` from `pipeline_exceptions` to the top-level imports section of the file.
2.  In [`main_pipeline.py`](main_pipeline.py), add a comprehensive module-level docstring at the beginning of the file. This docstring should clearly explain the module's purpose, its role in the HSI georeferencing pipeline, and an overview of its functionality (e.g., configuration loading, orchestration of processing steps).
3.  In `parse_hsi_header` function within [`georeference_hsi_pixels.py`](georeference_hsi_pixels.py), refactor the logic for parsing lever arm information. Implement a clear priority:
    *   Attempt to parse 'OffsetBetweenMainAntennaAndTargetPoint' first.
    *   If not found or parsing fails, then attempt to parse 'lever arm'.
    *   If both keys are found and successfully parsed, log a warning if their values differ significantly. Consistently use the value from 'OffsetBetweenMainAntennaAndTargetPoint' if both are present and valid.
4.  In the `parse_sensor_model` function within [`georeference_hsi_pixels.py`](georeference_hsi_pixels.py), enhance the logging when a conversion from degrees to radians occurs. The log message should be a warning and include the `max_abs_vinkelx` and `max_abs_vinkely` values that triggered the conversion, providing more context for users to identify potential misinterpretations. For example: `logger.warning(f"Detected angle values > 2π (max_abs_vinkelx={max_abs_vinkelx:.3f}, max_abs_vinkely={max_abs_vinkely:.3f}). Interpreting all angles as DEGREES and converting to radians. Verify sensor model format if this is unexpected.")`

### Expected Improvements
- Resolution of Issues 1, 2, 4, and 5 from [`reflection_LS4.md`](reflection_LS4.md).
- Improved code style and adherence to Python best practices in [`vectorized_georef.py`](vectorized_georef.py) and [`main_pipeline.py`](main_pipeline.py).
- Increased clarity and robustness in HSI header parsing and sensor model angle interpretation in [`georeference_hsi_pixels.py`](georeference_hsi_pixels.py).
- Enhanced maintainability of the affected modules.

---

## Prompt [LS5_2]

### Context
Issue 3 in [`reflection_LS4.md`](reflection_LS4.md) identifies the error handling for the `scipy.optimize.brentq` method within `calculate_ray_dsm_intersection` ([`georeference_hsi_pixels.py:303-358`](georeference_hsi_pixels.py:303-358)) as overly complex. This intricacy can hinder maintainability and make exhaustive testing difficult. Simplifying this logic is crucial for a more robust and understandable codebase.

### Objective
Simplify the error handling logic associated with `brentq` in the `calculate_ray_dsm_intersection` function in [`georeference_hsi_pixels.py`](georeference_hsi_pixels.py), making it more robust and easier to maintain, while ensuring correct behavior during ray marching.

### Focus Areas
- Reducing complexity of interval adjustment logic for `brentq`.
- Relying more on `brentq`'s native error reporting mechanisms.
- Clarifying and documenting the handling of grazing incidence.
- Ensuring ray marching proceeds correctly when `brentq` cannot be applied or fails.

### Code Reference
- [`georeference_hsi_pixels.py:303-358`](georeference_hsi_pixels.py:303-358) (specifically the `try-except` block for `brentq` and surrounding logic).

### Requirements
1.  Refactor the error handling for `brentq` within the `calculate_ray_dsm_intersection` function in [`georeference_hsi_pixels.py`](georeference_hsi_pixels.py).
2.  If `brentq` raises a `ValueError` (e.g., because `f(a)` and `f(b)` do not have opposite signs, or due to internal NaN issues from `func_to_solve`), the code should gracefully handle this. Instead of complex interval "nudging", the primary fallback should be to allow the ray marching process to continue (i.e., `continue` the loop) to search for a new, valid interval, or eventually fail if `max_dist` is reached.
3.  The specific check for `val_at_a * val_at_b > 0` (where `val_at_a = func_to_solve(a)` and `val_at_b = func_to_solve(b)`) indicates `brentq` is not applicable. The existing heuristic of checking `np.isclose(val_at_a, 0)` or `np.isclose(val_at_b, 0)` for grazing incidence is acceptable. This condition should be clearly documented in the code as a heuristic for handling cases where the ray grazes the DSM surface at one of the interval endpoints. If this condition is met, log the grazing incidence and continue ray marching.
4.  Ensure that if `func_to_solve(a)` or `func_to_solve(b)` initially returns `NaN`, the logic does not enter overly specific recovery attempts. The ray marching should proceed to find a segment where `func_to_solve` yields valid numbers at both ends.
5.  Add new unit tests or modify existing ones in [`test_georeferencing.py`](test_georeferencing.py) to specifically cover scenarios related to the simplified `brentq` error handling. These tests should include:
    *   Cases where `brentq` is expected to raise a `ValueError`.
    *   Scenarios where `func_to_solve` returns `NaN` at the interval boundaries.
    *   Grazing incidence conditions.
    *   Verification that ray marching continues correctly after such events.

### Expected Improvements
- Resolution of Issue 3 from [`reflection_LS4.md`](reflection_LS4.md).
- Significantly reduced complexity in the `calculate_ray_dsm_intersection` function.
- Improved maintainability and testability of the `brentq` error handling logic.
- More robust and predictable behavior of the ray-DSM intersection finding process, especially in edge cases.

---

## Prompt [LS5_3]

### Context
The [`scores_LS4.json`](scores_LS4.json) report indicates that overall test coverage is 65.0 (with reported line coverage at 57% and branch coverage at 52%), which is considerably below the project target of 80%. While modules like [`lever_arm_utils.py`](lever_arm_utils.py) (100%) and [`main_pipeline.py`](main_pipeline.py) (96%) have high coverage, others, particularly the core processing modules [`georeference_hsi_pixels.py`](georeference_hsi_pixels.py) and [`vectorized_georef.py`](vectorized_georef.py), likely have significant gaps. Improving test coverage is critical for ensuring code quality, reliability, and facilitating safer refactoring.

### Objective
Strategically increase test coverage across the project, with a primary focus on [`georeference_hsi_pixels.py`](georeference_hsi_pixels.py) and [`vectorized_georef.py`](vectorized_georef.py). The goal is to move overall line coverage closer to the 80% target and significantly improve branch coverage by targeting untested code paths, error handling, and edge cases.

### Focus Areas
- Identifying and testing uncovered functions, code blocks, and branches in [`georeference_hsi_pixels.py`](georeference_hsi_pixels.py).
- Identifying and testing uncovered functions, code blocks, and branches in [`vectorized_georef.py`](vectorized_georef.py).
- Specifically targeting error handling paths and edge case scenarios that are not yet covered by existing tests.
- Improving branch coverage by ensuring tests for different outcomes of conditional statements.

### Requirements
1.  Analyze current test coverage reports (e.g., using `coverage.py`) to pinpoint specific untested or undertested areas in [`georeference_hsi_pixels.py`](georeference_hsi_pixels.py) and [`vectorized_georef.py`](vectorized_georef.py).
2.  Write new unit tests in [`test_georeferencing.py`](test_georeferencing.py) (for `georeference_hsi_pixels.py`) and a corresponding test file (e.g., `test_vectorized_georef.py`, or consolidate if appropriate) for `vectorized_georef.py`.
3.  Prioritize the creation of tests that cover:
    *   Complex conditional logic (if/else statements, loops) to improve branch coverage.
    *   `try-except` blocks and other error/exception handling paths.
    *   Edge cases for input parameters (e.g., empty inputs, invalid values, zero values where not expected).
    *   Different operational modes or configurations if applicable (e.g., DSM vs. no-DSM processing, different interpolation settings).
4.  For [`georeference_hsi_pixels.py`](georeference_hsi_pixels.py), ensure more thorough testing of:
    *   `get_dsm_height_at_point`: Test with points inside, outside, and on the edge of the DSM; test different interpolation methods if configurable.
    *   `create_ray_dsm_difference_function` and `find_dsm_entry_point`: Verify their behavior with diverse inputs, especially if not fully covered by the tests added in LS4.
    *   The main georeferencing functions (`georeference_pixel_to_dsm`, `georeference_pixel_no_dsm`): Test with a wider range of pixel coordinates, sensor parameters, and pose data.
    *   Helper functions like `parse_hsi_header` and `parse_sensor_model` for more varied header/model file contents.
5.  For [`vectorized_georef.py`](vectorized_georef.py), ensure more thorough testing of:
    *   The `process_hsi_line_vectorized` function: Focus on the fallback mechanism to per-pixel processing for DSM intersections, the conditions that trigger it, and the correctness of both vectorized and fallback paths. Test with various HSI line data, pose information, and sensor models.
    *   Any internal helper functions used by `process_hsi_line_vectorized`.
6.  The aim is to increase overall line coverage by at least 10-15 percentage points from the current 57% and to achieve a noticeable improvement in branch coverage. Document newly covered areas or particularly tricky test cases with comments in the test files.

### Expected Improvements
- A significant increase in overall test line coverage, moving substantially closer to the 80% target.
- A significant increase in branch coverage, leading to more robust code.
- Increased confidence in the correctness and reliability of [`georeference_hsi_pixels.py`](georeference_hsi_pixels.py) and [`vectorized_georef.py`](vectorized_georef.py).
- Better ability to detect regressions and errors in future development cycles.
- Higher "coverage" score in subsequent evaluations.

---

## Prompt [LS5_4]

### Context
The performance score from [`scores_LS4.json`](scores_LS4.json) is 63.0, falling short of the 75 target. The [`reflection_LS4.md`](reflection_LS4.md) highlights two key optimization opportunities: micro-optimizations in `calculate_ray_dsm_intersection` ([`georeference_hsi_pixels.py`](georeference_hsi_pixels.py)) and, more impactfully, the vectorization of the DSM intersection part within `process_hsi_line_vectorized` ([`vectorized_georef.py`](vectorized_georef.py)). Addressing these could lead to substantial performance gains, especially for DSM-based georeferencing.

### Objective
Investigate and implement performance improvements for DSM-based georeferencing. This includes applying micro-optimizations to `calculate_ray_dsm_intersection` and, as a primary focus if feasible, attempting to vectorize the DSM intersection calculations currently performed per-pixel within `process_hsi_line_vectorized`.

### Focus Areas
- Identifying and applying micro-optimizations within the `calculate_ray_dsm_intersection` function in [`georeference_hsi_pixels.py`](georeference_hsi_pixels.py).
- Analyzing the feasibility of, and potentially implementing, a vectorized approach for the DSM intersection component of the `process_hsi_line_vectorized` function in [`vectorized_georef.py`](vectorized_georef.py).

### Code Reference
- [`georeference_hsi_pixels.py:256-365`](georeference_hsi_pixels.py:256-365) (`calculate_ray_dsm_intersection`)
- [`vectorized_georef.py:255-268`](vectorized_georef.py:255-268) (Fallback to per-pixel DSM intersection in `process_hsi_line_vectorized`)

### Requirements
1.  **Micro-optimizations for `calculate_ray_dsm_intersection`**:
    *   Carefully review the `calculate_ray_dsm_intersection` function in [`georeference_hsi_pixels.py`](georeference_hsi_pixels.py) for opportunities such as:
        *   Reducing redundant calculations or object creations within loops.
        *   Optimizing NumPy array operations (e.g., avoiding unnecessary copies, using more efficient indexing).
        *   Consider re-evaluating the adaptive step size logic for ray marching (mentioned as commented out in [`reflection_LS4.md`](reflection_LS4.md)) to see if it can provide benefits without adding excessive complexity.
    *   Any applied micro-optimizations must be benchmarked (e.g., using `timeit` or `cProfile` on representative data/scenarios) to confirm tangible performance improvements. Ensure accuracy is maintained by verifying against existing test cases.
2.  **Vectorization of DSM Intersection in `process_hsi_line_vectorized` (Exploratory & High Priority if Feasible)**:
    *   Thoroughly analyze the current DSM intersection logic that `process_hsi_line_vectorized` calls per pixel (currently falling back to `georeference_pixel_to_dsm` which uses `calculate_ray_dsm_intersection`).
    *   Investigate and attempt to implement a vectorized version of this DSM intersection process directly within or callable by `process_hsi_line_vectorized`. This is a challenging task due to the iterative nature of ray marching and root-finding algorithms like `brentq`.
    *   Consider strategies such as:
        *   Batch processing of rays if possible.
        *   Identifying parts of the intersection algorithm (e.g., initial ray setup, DSM lookups over a region) that can be vectorized even if the core iterative search remains per-ray but optimized.
        *   Exploring alternative vectorized ray-triangle or ray-grid intersection algorithms if adaptable to the DSM context, though this might be a larger research task.
    *   If full vectorization proves too complex or detrimental to maintainability for the current iteration, document the findings and attempt to vectorize any feasible sub-steps.
    *   If a vectorized approach is implemented, provide clear benchmarks demonstrating the performance gain over the per-pixel fallback. Ensure the solution is robust and maintains high accuracy.
3.  For all performance enhancements, ensure that:
    *   The correctness of the georeferencing results is not compromised. All existing unit and integration tests must pass.
    *   New tests might be required to validate the optimized code paths specifically.
    *   The code remains readable and maintainable. Add comments explaining complex optimizations.

### Expected Improvements
- A measurable improvement in the overall "performance" score.
- Significantly faster execution times for georeferencing operations when a DSM is used.
- Reduced processing time for large HSI datasets, making the pipeline more efficient.
- Insights into the feasibility and impact of vectorizing complex iterative geospatial algorithms.