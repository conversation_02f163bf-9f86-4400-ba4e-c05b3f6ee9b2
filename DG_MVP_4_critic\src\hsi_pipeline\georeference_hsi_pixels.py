"""
HSI pixel georeferencing module for the HSI Georeferencing Pipeline.

This module performs direct georeferencing of HSI pixels using pose data,
sensor models, and optional DSM intersection calculations.
"""

import pandas as pd
import numpy as np
from scipy.spatial.transform import Rotation
import os
import rasterio
import datetime
from scipy.interpolate import RegularGridInterpolator
from scipy.optimize import brentq
from typing import Dict, Any, Tuple, Optional

from .logging_config import get_logger
from .pipeline_exceptions import (
    GeoreferencingError, HSIDataError, PipelineConfigError, InputDataError,
    VectorizedProcessingError, PoseTransformationError
)
from .lever_arm_utils import determine_effective_lever_arm, validate_lever_arm
from .vectorized_georef import process_hsi_line_vectorized

# --- Coordinate System Definitions ---
# IMU-Body Coordinate System (Assumed FRD - Forward, Right, Down):
#   X-axis: Forward (in the direction of flight of the drone)
#   Y-axis: Right (from the pilot's perspective)
#   Z-axis: Down (towards the Earth)
#
# Sensor Coordinate System (Linescanner - Assumed):
#   Z_sensor-axis: Optical axis (ideally pointing downwards for nadir shots)
#   X_sensor-axis: Along the scan line (across-track)
#   Y_sensor-axis: Along the flight direction of the sensor (along-track, perpendicular to scan line)
#
# --- Positive Rotation Definitions (Right-Hand Rule) ---
# Based on the IMU-Body Coordinate System:
#   Positive Roll: Rotation around the X-body axis.
#   Positive Pitch: Rotation around the Y-body axis.
#   Positive Yaw: Rotation around the Z-body axis.
#
# --- Boresight Angles ---
# These angles define the rotation FROM the Body system TO the Sensor system.
# R_body_to_sensor = Rz(yaw_bs) @ Ry(pitch_bs) @ Rx(roll_bs)
# The script calculates R_sensor_to_body = R_body_to_sensor.T

def parse_hsi_header(hdr_file_path: str) -> Tuple[int, int, np.ndarray]:
    """
    Parse the HSI header file and extract 'samples', 'lines' and 'lever arm'.

    Args:
        hdr_file_path: Path to the .hdr file.

    Returns:
        Tuple containing:
            - samples (int): Number of samples per line.
            - lines (int): Number of lines.
            - lever_arm_body (np.array): Lever arm vector [x, y, z] in meters.

    Raises:
        HSIDataError: If header file cannot be parsed or required fields are missing
    """
    logger = get_logger(__name__)
    header_data = {}

    # Track lever arm parsing for priority logic
    offset_lever_arm = None
    lever_arm_key = None

    try:
        with open(hdr_file_path, 'r') as f:
            for line in f:
                line = line.strip()
                if '=' in line:
                    key, value = line.split('=', 1)
                    key = key.strip()
                    value = value.strip()
                    if key == 'samples':
                        header_data['samples'] = int(value)
                    elif key == 'lines':
                        header_data['lines'] = int(value)
                    elif key.startswith('OffsetBetweenMainAntennaAndTargetPoint'):
                        value = value.strip().replace('(', '').replace(')', '')
                        parts = value.split(',')
                        if len(parts) == 3:
                            # Convert from mm to meters
                            offset_lever_arm = np.array([float(p.strip()) / 1000.0 for p in parts])
                        else:
                            raise HSIDataError(f"Could not parse lever arm values from '{value}'")
                    elif key == 'lever arm':
                        value = value.replace('{', '').replace('}', '').replace(',', ' ')
                        lever_arm_key = np.array([float(v) for v in value.split()])

        # Apply priority logic for lever arm
        if offset_lever_arm is not None and lever_arm_key is not None:
            # Both keys found - check if values differ significantly
            if not np.allclose(offset_lever_arm, lever_arm_key, atol=1e-6):
                logger.warning(
                    f"Both 'OffsetBetweenMainAntennaAndTargetPoint' and 'lever arm' found in header "
                    f"with different values: {offset_lever_arm} vs {lever_arm_key}. "
                    f"Using value from 'OffsetBetweenMainAntennaAndTargetPoint'."
                )
            header_data['lever_arm'] = offset_lever_arm
        elif offset_lever_arm is not None:
            # Only OffsetBetweenMainAntennaAndTargetPoint found
            header_data['lever_arm'] = offset_lever_arm
        elif lever_arm_key is not None:
            # Only lever arm key found
            header_data['lever_arm'] = lever_arm_key

    except FileNotFoundError:
        raise InputDataError(f"HSI header file not found: {hdr_file_path}") from None
    except Exception as e:
        raise HSIDataError(f"Error reading HSI header file {hdr_file_path}: {e}") from e

    if 'samples' not in header_data:
        raise HSIDataError("'samples' not found in header file")
    if 'lines' not in header_data:
        raise HSIDataError("'lines' not found in header file")
    if 'lever_arm' not in header_data:
        logger.warning("No valid 'lever arm' or 'OffsetBetweenMainAntennaAndTargetPoint' entry found in header file")
        # Return zero lever arm as default
        header_data['lever_arm'] = np.array([0.0, 0.0, 0.0])

    return header_data['samples'], header_data['lines'], header_data['lever_arm']

def parse_sensor_model(sensor_model_path: str, num_samples: int) -> Tuple[np.ndarray, np.ndarray]:
    """
    Parse the sensor angle model.

    Angles are interpreted as radians.

    Args:
        sensor_model_path: Path to sensor model file
        num_samples: Expected number of samples

    Returns:
        Tuple of (vinkelx_rad, vinkely_rad) arrays

    Raises:
        HSIDataError: If sensor model file cannot be parsed
    """
    logger = get_logger(__name__)

    try:
        sensor_data = pd.read_csv(sensor_model_path, sep=r'\s+', header=None, skiprows=1,
                                  names=['pixel_index', 'vinkelx_deg', 'vinkely_deg'])
    except Exception as e:
        try:
            sensor_data = pd.read_csv(sensor_model_path, sep=r'\s+', header=None,
                                      names=['pixel_index', 'vinkelx_deg', 'vinkely_deg'])
        except Exception as e2:
            try:
                sensor_data_raw = pd.read_csv(sensor_model_path, sep=r'\s+', header=None,
                                          names=['vinkelx_deg', 'vinkely_deg'])
                sensor_data = pd.DataFrame({
                    'pixel_index': np.arange(len(sensor_data_raw)),
                    'vinkelx_deg': sensor_data_raw['vinkelx_deg'],
                    'vinkely_deg': sensor_data_raw['vinkely_deg']
                })
            except Exception as final_e:
                raise HSIDataError(f"Could not parse sensor model file: {sensor_model_path}. Error: {final_e}") from final_e

    if len(sensor_data) != num_samples:
        logger.warning(f"Number of entries in sensor model ({len(sensor_data)}) does not match 'samples' ({num_samples})")
        if len(sensor_data) < num_samples:
             raise HSIDataError(f"Too few entries in sensor model. Expected: {num_samples}, Found: {len(sensor_data)}")
        sensor_data = sensor_data.iloc[:num_samples]

    # LS3_3: Improved angle interpretation logic
    # Check if values are likely in degrees based on magnitude
    vinkelx_values = sensor_data['vinkelx_deg'].values
    vinkely_values = sensor_data['vinkely_deg'].values

    # Heuristic: if any absolute value > 2*pi (≈6.28), likely degrees
    max_abs_vinkelx = np.max(np.abs(vinkelx_values))
    max_abs_vinkely = np.max(np.abs(vinkely_values))

    if max_abs_vinkelx > 2 * np.pi or max_abs_vinkely > 2 * np.pi:
        logger.warning(
            f"Detected angle values > 2π (max_abs_vinkelx={max_abs_vinkelx:.3f}, "
            f"max_abs_vinkely={max_abs_vinkely:.3f}). Interpreting all angles as DEGREES "
            f"and converting to radians. Verify sensor model format if this is unexpected."
        )
        vinkelx_rad = np.deg2rad(vinkelx_values)
        vinkely_rad = np.deg2rad(vinkely_values)
    else:
        logger.info("Interpreting 'vinkelx_deg' and 'vinkely_deg' columns from sensor model as RADIANS directly")
        vinkelx_rad = vinkelx_values
        vinkely_rad = vinkely_values

    return vinkelx_rad, vinkely_rad

def get_dsm_height_at_point(x: float, y: float, interpolator, bounds, nodata_value: float) -> float:
    """
    Helper function to get DSM height at a specific point (x, y).

    This function safely queries the DSM interpolator while handling boundary
    conditions and nodata values. It's designed to be robust against edge cases
    that can occur during ray-DSM intersection calculations.

    Args:
        x (float): X-coordinate to query (in DSM coordinate system)
        y (float): Y-coordinate to query (in DSM coordinate system)
        interpolator: DSM interpolator object (RegularGridInterpolator)
        bounds: DSM bounds object with left, right, bottom, top attributes
        nodata_value (float): Value representing no data in the DSM

    Returns:
        float: DSM height at (x, y) or np.nan if outside bounds or nodata

    Note:
        The interpolator expects (y, x) order for coordinates, which is handled
        internally by this function.
    """
    if not (bounds.left <= x <= bounds.right and bounds.bottom <= y <= bounds.top):
        return np.nan
    z_val = interpolator((y, x))  # Interpolator expects (y, x), returns scalar
    # Check against nodata_value only if nodata_value itself is not NaN
    if nodata_value is not None and not np.isnan(nodata_value) and np.isclose(z_val, nodata_value):
        return np.nan
    # If nodata_value is np.nan, interpolator's fill_value=np.nan handles it.
    # If z_val is already np.nan (e.g. from interpolator fill_value), it's fine.
    return z_val


def create_ray_dsm_difference_function(P_sensor, d_world_normalized, interpolator, bounds, nodata_value):
    """
    Helper function to create the difference function for ray-DSM intersection.

    Args:
        P_sensor: Sensor position
        d_world_normalized: Normalized ray direction
        interpolator: DSM interpolator object
        bounds: DSM bounds object
        nodata_value: Value representing no data

    Returns:
        function: Function that computes ray_z(t) - dsm_z(ray_xy(t))
    """
    def func_to_solve(t):
        P_intersect = P_sensor + t * d_world_normalized
        x_ray, y_ray, z_ray = P_intersect[0], P_intersect[1], P_intersect[2]

        z_dsm = get_dsm_height_at_point(x_ray, y_ray, interpolator, bounds, nodata_value)
        if np.isnan(z_dsm):
            # Heuristic: return a value indicating the ray is "above" an arbitrary low plane
            # to guide brentq if it encounters NaN during its search.
            return z_ray - (P_sensor[2] - 10000)  # Large difference
        return z_ray - z_dsm

    return func_to_solve


def find_dsm_entry_point(P_sensor, d_world_normalized, interpolator, bounds, nodata_value, initial_step, max_dist):
    """
    Helper function to find a valid entry point on the DSM for ray marching.

    Args:
        P_sensor: Sensor position
        d_world_normalized: Normalized ray direction
        interpolator: DSM interpolator object
        bounds: DSM bounds object
        nodata_value: Value representing no data
        initial_step: Initial step size for searching
        max_dist: Maximum search distance

    Returns:
        tuple: (t_entry, z_ray_entry, z_dsm_entry) or (None, None, None) if no entry found
    """
    for t_entry_candidate in np.arange(initial_step, max_dist, initial_step):
        p_candidate = P_sensor + t_entry_candidate * d_world_normalized
        z_dsm_candidate = get_dsm_height_at_point(p_candidate[0], p_candidate[1], interpolator, bounds, nodata_value)
        if not np.isnan(z_dsm_candidate):
            # Found a valid point on DSM along the ray
            return t_entry_candidate, p_candidate[2], z_dsm_candidate
    return None, None, None


def calculate_ray_dsm_intersection(P_sensor, d_world_normalized, interpolator, bounds, nodata_value, max_dist, initial_step, tolerance):
    """
    Calculates the intersection of a 3D ray with a Digital Surface Model (DSM).

    This function has been refactored to use helper functions for better maintainability.
    """
    # Create the difference function for intersection solving
    func_to_solve = create_ray_dsm_difference_function(P_sensor, d_world_normalized, interpolator, bounds, nodata_value)

    # Initial point on ray (sensor position)
    z_ray_start = P_sensor[2]
    z_dsm_start = get_dsm_height_at_point(P_sensor[0], P_sensor[1], interpolator, bounds, nodata_value)

    t_current = 0.0  # Parameter t along the ray

    # If starting point is already outside DSM or on nodata, try to find an entry point
    if np.isnan(z_dsm_start):
        t_entry, z_ray_entry, z_dsm_entry = find_dsm_entry_point(
            P_sensor, d_world_normalized, interpolator, bounds, nodata_value, initial_step, max_dist
        )
        if t_entry is None:
            return np.nan, np.nan, np.nan  # No valid entry point found

        t_current = t_entry
        z_ray_start = z_ray_entry
        z_dsm_start = z_dsm_entry

    # We have a valid starting point (z_ray_start, z_dsm_start) at t_current
    diff_prev = z_ray_start - z_dsm_start
    t_prev = t_current

    # LS5_4: Performance optimizations - cache frequently accessed values
    bounds_left, bounds_right = bounds.left, bounds.right
    bounds_bottom, bounds_top = bounds.bottom, bounds.top
    d_x, d_y, d_z = d_world_normalized  # Unpack for faster access
    P_x, P_y, P_z = P_sensor

    # Ray Marching Loop
    step = initial_step
    t_search = t_current

    while t_search <= max_dist:
        t_search += step

        # LS5_4: Optimized ray position calculation using unpacked components
        x_ray_curr = P_x + t_search * d_x
        y_ray_curr = P_y + t_search * d_y
        z_ray_curr = P_z + t_search * d_z

        # LS5_4: Optimized bounds checking with cached values
        if not (bounds_left <= x_ray_curr <= bounds_right and
                bounds_bottom <= y_ray_curr <= bounds_top):
            return np.nan, np.nan, np.nan

        z_dsm_curr = get_dsm_height_at_point(x_ray_curr, y_ray_curr, interpolator, bounds, nodata_value)

        if np.isnan(z_dsm_curr): # Ray exited DSM valid area or hit nodata
            return np.nan, np.nan, np.nan

        diff_curr = z_ray_curr - z_dsm_curr

        if diff_prev * diff_curr <= 0: # Sign change indicates intersection in [t_prev, t_search]
            try:
                # Simplified brentq error handling (LS5_2)
                # Check for basic validity before calling brentq
                val_at_a = func_to_solve(t_prev)
                val_at_b = func_to_solve(t_search)

                # Skip if either endpoint is NaN or if no sign change
                if np.isnan(val_at_a) or np.isnan(val_at_b) or val_at_a * val_at_b > 0:
                    diff_prev = diff_curr
                    t_prev = t_search
                    continue

                # Call brentq with valid bracket
                t_intersect = brentq(func_to_solve, t_prev, t_search, xtol=tolerance, rtol=tolerance)
                # LS5_4: Optimized final position calculation
                return (P_x + t_intersect * d_x, P_y + t_intersect * d_y, P_z + t_intersect * d_z)

            except (ValueError, RuntimeError):
                # brentq failed - continue ray marching
                pass
            except Exception:
                # Unexpected error - give up on this ray
                return np.nan, np.nan, np.nan

        diff_prev = diff_curr
        t_prev = t_search

        # LS5_4: Adaptive step size for better performance
        if abs(diff_curr) < 0.1:  # Close to intersection
            step = max(tolerance * 10, step * 0.5)  # Reduce step size
        elif abs(diff_curr) > 10.0:  # Far from intersection
            step = min(initial_step * 2, step * 1.5)  # Increase step size

    return np.nan, np.nan, np.nan # No intersection found within max_dist

def run_georeferencing(config: dict, config_file_path: Optional[str] = None) -> bool:
    """
    Execute HSI pixel georeferencing based on the provided configuration.

    Args:
        config: Configuration dictionary loaded from TOML file
        config_file_path: Optional path to the config file for resolving relative paths

    Returns:
        True if georeferencing completed successfully, False otherwise

    Raises:
        GeoreferencingError: If georeferencing process fails
        HSIDataError: If HSI data cannot be processed
        PipelineConfigError: If configuration is invalid
    """


    logger = get_logger(__name__)
    logger.info("Starting HSI pixel georeferencing")

    logger.info("Reading path configuration...")
    try:
        hsi_data_dir = config['paths']['hsi_data_directory']
        hsi_base_filename = config['paths']['hsi_base_filename']
        hdr_file_path = os.path.join(hsi_data_dir, f"{hsi_base_filename}.hdr")

        sensor_model_filename = config['paths']['sensor_model_file']
        sensor_model_path = os.path.join(hsi_data_dir, sensor_model_filename)

        output_dir = config['paths']['output_directory']
        os.makedirs(output_dir, exist_ok=True)
        logger.info(f"Output directory: {output_dir} (created if not exists)")

        hsi_poses_input_filename = config['paths']['hsi_poses_csv']
        poses_file_path = os.path.join(output_dir, hsi_poses_input_filename)
        logger.info(f"HSI poses CSV (expected in output_dir): {poses_file_path}")

        base_output_filename = config['paths']['georeferenced_pixels_csv']
        output_file_path = os.path.join(output_dir, base_output_filename)
        logger.info(f"Output georeferenced pixels CSV: {output_file_path}")

    except KeyError as e:
        raise PipelineConfigError(f"Missing key in configuration file [paths]: {e}") from e

    logger.info("Reading georeferencing parameters...")
    try:
        boresight_roll_deg = config['parameters']['georeferencing']['boresight_roll_deg']
        boresight_pitch_deg = config['parameters']['georeferencing']['boresight_pitch_deg']
        boresight_yaw_deg = config['parameters']['georeferencing']['boresight_yaw_deg']
        logger.info(f"Boresight angles (degrees): Roll={boresight_roll_deg}, Pitch={boresight_pitch_deg}, Yaw={boresight_yaw_deg}")

        z_ground_method = config['parameters']['georeferencing']['z_ground_calculation_method']
        z_ground_offset = config['parameters']['georeferencing']['z_ground_offset_meters']
        z_ground_fixed = config['parameters']['georeferencing']['z_ground_fixed_value_meters']

        dsm_interpolator = None
        dsm_bounds = None
        dsm_nodata_value = np.nan  # Default to np.nan if not specified or None in GeoTIFF
        ray_max_dist = config['parameters']['georeferencing'].get('ray_dsm_max_search_dist_m', 2000.0)
        ray_initial_step = config['parameters']['georeferencing'].get('ray_dsm_step_m', 5.0)
        ray_bisection_tol = config['parameters']['georeferencing'].get('ray_dsm_bisection_tolerance_m', 0.01)

        # Sensor model correction parameters
        scale_vinkel_x = config['parameters']['georeferencing'].get('scale_vinkel_x', 1.0)
        offset_vinkel_x = config['parameters']['georeferencing'].get('offset_vinkel_x', 0.0)
        logger.info(f"Sensor model correction: scale_vinkel_x={scale_vinkel_x}, offset_vinkel_x={offset_vinkel_x}")

        # Lever arm parameters from config (will be processed after reading HDR)
        config_lever_arm_x = config['parameters']['georeferencing'].get('lever_arm_x_m', 0.0)
        config_lever_arm_y = config['parameters']['georeferencing'].get('lever_arm_y_m', 0.0)
        config_lever_arm_z = config['parameters']['georeferencing'].get('lever_arm_z_m', 0.0)
        config_lever_arm = np.array([config_lever_arm_x, config_lever_arm_y, config_lever_arm_z])

        logger.info(f"Z_ground method: {z_ground_method}, Offset: {z_ground_offset}, Fixed value: {z_ground_fixed}")
        if z_ground_method == "dsm_intersection":
            logger.info(f"Ray marching params: MaxDist={ray_max_dist}m, Step={ray_initial_step}m, Tol={ray_bisection_tol}m")

    except KeyError as e:
        raise PipelineConfigError(f"Missing key in configuration file [parameters.georeferencing]: {e}") from e

    if z_ground_method == "dsm_intersection":
        logger.info("Loading DSM for 'dsm_intersection' method...")
        try:
            dsm_file_path_from_config = config['paths']['dsm_file']
            # LS6_3: Resolve DSM path relative to config file location if provided
            dsm_path = dsm_file_path_from_config
            if not os.path.isabs(dsm_path):
                if config_file_path:
                    # Resolve relative to config file directory
                    config_dir = os.path.dirname(os.path.abspath(config_file_path))
                    dsm_path = os.path.join(config_dir, dsm_path)
                    logger.info(f"Resolved relative DSM path '{dsm_file_path_from_config}' relative to config file directory: {dsm_path}")
                else:
                    # Fallback to current working directory (original behavior)
                    dsm_path = os.path.join(os.getcwd(), dsm_path)
                    logger.warning(f"No config file path provided, resolving DSM path relative to current working directory: {dsm_path}")

            logger.info(f"Attempting to open DSM: {dsm_path}")
            with rasterio.open(dsm_path) as src:
                dsm_array = src.read(1).astype(np.float32)
                dsm_transform = src.transform
                # Use src.nodatavals[0] if available and not None, otherwise keep dsm_nodata_value as np.nan
                if src.nodatavals and src.nodatavals[0] is not None:
                    dsm_nodata_value = float(src.nodatavals[0])
                dsm_bounds = src.bounds
                logger.info(f"DSM successfully loaded. Shape: {dsm_array.shape}, NoData: {dsm_nodata_value}")
                logger.info(f"DSM Transform: {dsm_transform}")
                logger.info(f"DSM Bounds: {dsm_bounds}")

            # Note: cols and rows variables are not used, removing them
            x_coords_dsm = np.array([dsm_transform.c + dsm_transform.a * (i + 0.5) for i in range(dsm_array.shape[1])])
            y_coords_dsm_raw = np.array([dsm_transform.f + dsm_transform.e * (i + 0.5) for i in range(dsm_array.shape[0])])

            dsm_array_for_interp = np.copy(dsm_array) # Work on a copy

            if dsm_transform.e < 0:
                y_coords_dsm_asc = y_coords_dsm_raw[::-1]
                dsm_array_for_interp = dsm_array_for_interp[::-1, :]
            else:
                y_coords_dsm_asc = y_coords_dsm_raw

            if not np.isnan(dsm_nodata_value): # Only replace if nodata_value is a specific number
                nodata_mask = np.isclose(dsm_array_for_interp, dsm_nodata_value)
                dsm_array_for_interp[nodata_mask] = np.nan

            # Ensure all actual NaNs (either original or converted nodata) are handled by fill_value
            # This is implicitly handled by fill_value=np.nan in RegularGridInterpolator

            dsm_interpolator = RegularGridInterpolator(
                (y_coords_dsm_asc, x_coords_dsm), dsm_array_for_interp,
                method='linear', bounds_error=False, fill_value=np.nan # Changed 'bilinear' to 'linear'
            )
            logger.info("DSM Interpolator (RegularGridInterpolator) successfully initialized")

        except FileNotFoundError:
            raise InputDataError(f"DSM file not found: {dsm_path}") from None
        except rasterio.errors.RasterioIOError as e:
            raise GeoreferencingError(f"Rasterio could not open or read DSM file: {dsm_path}. Error: {e}") from e
        except KeyError as e:
            raise PipelineConfigError(f"Missing key for DSM path in configuration file: {e}") from e
        except Exception as e:
            raise GeoreferencingError(f"Unexpected error loading or processing DSM: {e}") from e

    logger.info(f"Reading HSI header file: {hdr_file_path}")
    try:
        num_samples_hdr, num_lines_hdr, lever_arm_from_hdr = parse_hsi_header(hdr_file_path)
    except ValueError as e:
        raise HSIDataError(f"Error parsing HSI header file: {e}") from e

    logger.info(f"Header info: Samples: {num_samples_hdr}, Lines: {num_lines_hdr}, Lever arm from HDR: {lever_arm_from_hdr}")

    # Determine effective lever arm using the new logic
    effective_lever_arm_body, config_override_used = determine_effective_lever_arm(
        lever_arm_from_hdr, config_lever_arm
    )

    # Validate the effective lever arm
    if not validate_lever_arm(effective_lever_arm_body):
        raise GeoreferencingError("Invalid effective lever arm determined")

    logger.info(f"Effective lever arm (m) to be used in calculations: {effective_lever_arm_body}")
    if config_override_used:
        logger.info("Using lever arm from configuration override")
    else:
        logger.info("Using lever arm from HSI header")

    # Use num_samples and num_lines from header for consistency
    num_samples = num_samples_hdr
    num_lines = num_lines_hdr

    logger.info(f"Reading sensor angle model: {sensor_model_path}")
    try:
        vinkelx_rad_all_pixels, vinkely_rad_all_pixels = parse_sensor_model(sensor_model_path, num_samples)
    except HSIDataError as e:
        logger.error(f"Error parsing sensor model file: {e}")
        raise
    logger.info(f"Sensor angles for {len(vinkelx_rad_all_pixels)} pixels loaded")
    logger.info(f"Original vinkelx_rad (treated as rad) range: min={np.min(vinkelx_rad_all_pixels):.3f} rad, max={np.max(vinkelx_rad_all_pixels):.3f} rad")
    logger.info(f"Equivalent degrees: min={np.rad2deg(np.min(vinkelx_rad_all_pixels)):.2f} deg, max={np.rad2deg(np.max(vinkelx_rad_all_pixels)):.2f} deg")

    logger.info(f"Reading poses file: {poses_file_path}")
    try:
        poses_df = pd.read_csv(poses_file_path)
    except FileNotFoundError:
        raise InputDataError(f"Poses file not found: {poses_file_path}") from None
    except Exception as e:
        raise GeoreferencingError(f"Poses file could not be read {poses_file_path}: {e}") from e
    logger.info(f"{len(poses_df)} poses loaded")

    if len(poses_df) != num_lines:
        raise GeoreferencingError(f"Number of poses ({len(poses_df)}) does not match number of HSI lines ({num_lines})")
    
    # Z_ground calculation for non-DSM methods
    Z_ground_flat_plane = np.nan
    default_fallback_z_ground = z_ground_fixed

    if 'pos_z' in poses_df.columns and poses_df['pos_z'].notna().any():
        avg_pose_z_val = poses_df['pos_z'].mean()
        default_fallback_z_ground = avg_pose_z_val - 20.0
    else:
        logger.warning("Column 'pos_z' not found in poses DataFrame or contains only NaNs")
        if z_ground_method == "avg_pose_z_minus_offset":
            raise GeoreferencingError(f"z_ground_method is '{z_ground_method}', but 'pos_z' is missing. Cannot calculate Z_ground")

    if z_ground_method == "avg_pose_z_minus_offset":
        if 'pos_z' in poses_df.columns and poses_df['pos_z'].notna().any():
            avg_pose_z = poses_df['pos_z'].mean()
            Z_ground_flat_plane = avg_pose_z - z_ground_offset
            logger.info(f"Z_ground (flat plane) method: 'avg_pose_z_minus_offset'. Avg Z: {avg_pose_z:.3f} m, Offset: {z_ground_offset} m")
        else: # Should have been caught
            Z_ground_flat_plane = default_fallback_z_ground
    elif z_ground_method == "fixed_value":
        Z_ground_flat_plane = z_ground_fixed
        logger.info("Z_ground (flat plane) method: 'fixed_value'")
    elif z_ground_method == "dsm_intersection":
        if dsm_interpolator is None:
            raise GeoreferencingError("z_ground_method is 'dsm_intersection', but DSM interpolator was not initialized")
        logger.info("Z_ground method: 'dsm_intersection'. Z_ground will be calculated per ray from DSM")
    else:
        # LS7_4: Raise PipelineConfigError for unknown z_ground_calculation_method
        valid_methods = ["avg_pose_z_minus_offset", "fixed_value", "dsm_intersection"]
        raise PipelineConfigError(
            f"Unknown z_ground_calculation_method: '{z_ground_method}'. "
            f"Valid options are: {', '.join(valid_methods)}"
        )

    if z_ground_method != "dsm_intersection":
        if np.isnan(Z_ground_flat_plane):
            raise GeoreferencingError("Z_ground_flat_plane could not be determined")
        logger.info(f"Using Z_ground_flat_plane for projection (non-DSM methods): {Z_ground_flat_plane:.2f} meters")
    
    boresight_deg_array_for_body_to_sensor = np.array([-boresight_yaw_deg, -boresight_roll_deg, boresight_pitch_deg])
    R_body_to_sensor = Rotation.from_euler('zyx', boresight_deg_array_for_body_to_sensor, degrees=True).as_matrix()
    R_sensor_to_body = R_body_to_sensor.T

    logger.info(f"Boresight angles (degrees) used: Roll={boresight_roll_deg}, Pitch={boresight_pitch_deg}, Yaw={boresight_yaw_deg}")
    logger.debug(f"R_sensor_to_body (Sensor -> Body):\n{R_sensor_to_body}")

    debug_lines = {0, num_lines // 2, num_lines - 1}
    debug_pixels = {0, num_samples // 2, num_samples - 1}
    logger.info(f"Debugging output enabled for lines: {debug_lines}, pixels: {debug_pixels}")

    # For sensor model correction debug print, only print for the very first pixel of the first line once
    first_pixel_debug_printed = False
    
    results = []
    nan_intersection_count = 0
    d_world_z_threshold = 1e-6

    logger.info("Starting main georeferencing processing loop...")

    for i in range(num_lines):
        if i % 100 == 0:
            logger.info(f"Processing HSI line {i+1}/{num_lines}...")

        pose_index_to_use = num_lines - 1 - i
        current_pose = poses_df.iloc[pose_index_to_use]
        if i in debug_lines:
             logger.debug(f"For HSI line {i}, using pose index {pose_index_to_use}. Timestamp: {current_pose.get('timestamp', 'N/A')}")

        P_imu_world = np.array([current_pose['pos_x'], current_pose['pos_y'], current_pose['pos_z']])
        q_body_to_world_xyzw = np.array([current_pose['quat_x'], current_pose['quat_y'],
                                         current_pose['quat_z'], current_pose['quat_w']])
        try:
            R_body_to_world = Rotation.from_quat(q_body_to_world_xyzw).as_matrix()
            R_sensor_to_world = R_body_to_world @ R_sensor_to_body
        except Exception as e:
            logger.error(f"Error in quaternion conversion for line {i}: {q_body_to_world_xyzw}, Error: {e}")
            for j_err in range(num_samples):
                 results.append({'hsi_line_index': i, 'pixel_index': j_err, 'X_ground': np.nan, 'Y_ground': np.nan, 'Z_ground': np.nan})
            continue

        P_sensor_world = P_imu_world + R_body_to_world @ effective_lever_arm_body # Use effective_lever_arm_body

        # LS2_2: Use vectorized processing for flat-plane method
        if z_ground_method != "dsm_intersection":
            # Use vectorized function for flat-plane calculations
            try:
                # LS6_1: Pass raw pose data instead of pre-calculated matrices
                pose_data = {
                    'pos_x': current_pose['pos_x'],
                    'pos_y': current_pose['pos_y'],
                    'pos_z': current_pose['pos_z'],
                    'quat_x': current_pose['quat_x'],
                    'quat_y': current_pose['quat_y'],
                    'quat_z': current_pose['quat_z'],
                    'quat_w': current_pose['quat_w']
                }

                line_results = process_hsi_line_vectorized(
                    line_index=i,
                    pose_data=pose_data,
                    num_samples=num_samples,
                    vinkelx_rad_all=vinkelx_rad_all_pixels,
                    vinkely_rad_all=vinkely_rad_all_pixels,
                    R_sensor_to_body=R_sensor_to_body,
                    effective_lever_arm_body=effective_lever_arm_body,
                    scale_vinkel_x=scale_vinkel_x,
                    offset_vinkel_x=offset_vinkel_x,
                    z_ground_method="flat_plane",
                    z_ground_flat_plane=Z_ground_flat_plane
                )

                # LS6_1: Convert vectorized results (List[Dict]) to the expected format
                for pixel_data_dict in line_results:
                    results.append({
                        'hsi_line_index': pixel_data_dict['hsi_line_index'],
                        'pixel_index': pixel_data_dict['pixel_index'],
                        'X_ground': pixel_data_dict['X_ground'],
                        'Y_ground': pixel_data_dict['Y_ground'],
                        'Z_ground': pixel_data_dict['Z_ground']
                    })

                    if np.isnan(pixel_data_dict['X_ground']):
                        nan_intersection_count += 1

                # Debug output for vectorized processing
                if i in debug_lines:
                    valid_count = sum(1 for pixel_dict in line_results if not np.isnan(pixel_dict['X_ground']))
                    logger.debug(f"Vectorized processing for line {i}: {valid_count} valid intersections")

                continue  # Skip the individual pixel processing loop

            except (VectorizedProcessingError, PoseTransformationError) as e:
                logger.warning(f"Vectorized processing failed for line {i} due to {type(e).__name__}: {e}. Falling back to individual pixel processing")
                # Fall through to individual pixel processing

            except Exception as e:
                logger.warning(f"Unexpected error in vectorized processing for line {i}: {e}. Falling back to individual pixel processing")
                # Fall through to individual pixel processing

        # Individual pixel processing (for DSM intersection or fallback)
        for j in range(num_samples):
            original_vinkelx_rad = vinkelx_rad_all_pixels[j] # Renamed
            vinkely_rad = vinkely_rad_all_pixels[j]

            # Apply sensor model correction for vinkelx_rad
            corrected_vinkelx_rad = (original_vinkelx_rad * scale_vinkel_x) + offset_vinkel_x

            if not first_pixel_debug_printed and i == 0 and j == 0:
                logger.debug(f"Sensor Correction - Loaded scale_vinkel_x: {scale_vinkel_x}, offset_vinkel_x: {offset_vinkel_x}")
                logger.debug(f"Sensor Correction - Pixel (L{i},P{j}): Original vinkelx_rad: {original_vinkelx_rad:.6f}, Corrected vinkelx_rad: {corrected_vinkelx_rad:.6f}")
                first_pixel_debug_printed = True

            # Use corrected_vinkelx_rad for d_sensor_frame calculation
            dx = np.sin(corrected_vinkelx_rad) * np.cos(vinkely_rad)
            dy = np.sin(vinkely_rad) # vinkely_rad is not corrected by these parameters
            dz = np.cos(corrected_vinkelx_rad) * np.cos(vinkely_rad)
            d_sensor_frame = np.array([dx, dy, dz])

            norm_d_sensor_frame = np.linalg.norm(d_sensor_frame)
            if not np.isclose(norm_d_sensor_frame, 1.0):
                if norm_d_sensor_frame > 1e-9:
                    d_sensor_frame = d_sensor_frame / norm_d_sensor_frame
                else:
                    d_sensor_frame = np.array([0,0,1])

            d_world = R_sensor_to_world @ d_sensor_frame

            X_ground, Y_ground, Z_ground_coord = np.nan, np.nan, np.nan

            if z_ground_method == "dsm_intersection":
                if dsm_interpolator and dsm_bounds:
                    X_ground, Y_ground, Z_ground_coord = calculate_ray_dsm_intersection(
                        P_sensor_world, d_world, dsm_interpolator, dsm_bounds, dsm_nodata_value,
                        ray_max_dist, ray_initial_step, ray_bisection_tol
                    )
                    if np.isnan(X_ground): # If X is NaN, assume intersection failed
                        nan_intersection_count += 1
                else: # Should not happen if check for dsm_interpolator was done before loop
                    nan_intersection_count += 1
            else:
                # Fallback flat-plane calculation (should rarely be used due to vectorization)
                if abs(d_world[2]) > d_world_z_threshold:
                    t = (Z_ground_flat_plane - P_sensor_world[2]) / d_world[2]
                    if t >= 0:
                        X_ground = P_sensor_world[0] + t * d_world[0]
                        Y_ground = P_sensor_world[1] + t * d_world[1]
                        Z_ground_coord = Z_ground_flat_plane
                    else:
                        nan_intersection_count +=1
                else:
                    nan_intersection_count +=1

            results.append({
                'hsi_line_index': i,
                'pixel_index': j,
                'X_ground': X_ground,
                'Y_ground': Y_ground,
                'Z_ground': Z_ground_coord
            })

            if i in debug_lines and j in debug_pixels:
                if j == min(list(debug_pixels)) if debug_pixels else False:
                    logger.debug(f"\n--- Debugging HSI Line {i} (Pose Index {pose_index_to_use}) ---")
                    logger.debug(f"  P_sensor_world: {P_sensor_world}")
                    logger.debug(f"  R_sensor_to_world:\n{R_sensor_to_world}")
                logger.debug(f"    --- Pixel {j} ---")
                logger.debug(f"    orig_vx_rad: {original_vinkelx_rad:.5f}, corr_vx_rad={corrected_vinkelx_rad:.5f}, vinkely_rad: {vinkely_rad:.5f}")
                logger.debug(f"    d_sensor_frame (using corrected_vinkelx): {d_sensor_frame}")
                logger.debug(f"    d_world: {d_world}")
                logger.debug(f"    Intersection (X,Y,Z): ({X_ground:.3f}, {Y_ground:.3f}, {Z_ground_coord:.3f})")

    results_df = pd.DataFrame(results)
    logger.info(f"Processing completed. {nan_intersection_count} of {num_lines*num_samples} pixels could not be projected to ground (NaN)")

    # Calculate and log mean yaw angle from poses
    if 'yaw_deg' in poses_df.columns:
        mean_yaw_deg = poses_df['yaw_deg'].mean()
        logger.info(f"Mean yaw angle from poses: {mean_yaw_deg:.2f} degrees")
    else:
        logger.warning("Column 'yaw_deg' not found in poses DataFrame. Mean yaw cannot be calculated")

    logger.info(f"Saving georeferenced pixel coordinates to: {output_file_path}")
    try:
        results_df.to_csv(output_file_path, index=False, float_format='%.5f')
        logger.info("File successfully saved")
        return True # Success
    except Exception as e:
        raise GeoreferencingError(f"Error saving CSV file: {e}") from e


if __name__ == "__main__":
    """
    Command-line interface for HSI pixel georeferencing.

    This allows the module to be run independently for testing purposes.
    In production, this function should be called from the main pipeline.
    """
    import toml
    from logging_config import setup_logging

    DEFAULT_CONFIG_PATH = 'config.toml'

    # Setup logging for standalone execution
    setup_logging(log_level="INFO", log_file="georeferencing.log")
    logger = get_logger(__name__)

    logger.info(f"Running direct HSI pixel georeferencing with configuration: {DEFAULT_CONFIG_PATH}")

    try:
        # Load configuration
        config = toml.load(DEFAULT_CONFIG_PATH)

        # Run georeferencing
        success = run_georeferencing(config)

        if success:
            logger.info("Direct HSI pixel georeferencing completed successfully")
        else:
            logger.error("Direct HSI pixel georeferencing failed")

    except Exception as e:
        logger.error(f"Error during HSI pixel georeferencing: {e}", exc_info=True)