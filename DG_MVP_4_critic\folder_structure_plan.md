## Plan to Improve Project Folder Structure for HSI Georeferencing Pipeline

### 1. Introduction and Current Situation

The HSI Georeferencing Pipeline project has evolved through multiple iterative development layers (LS1-LS6+). This has resulted in many Python scripts, test files, aiGI workflow artifacts (prompts, reflections, scores, test specifications), and various output files residing at the project's root level. While dedicated directories like `data/`, `research/`, and `plots/` exist, the overall structure can be improved for better organization, maintainability, and clarity. This plan outlines a new folder structure to address these points.

### 2. Proposed New Folder Structure

The proposed structure adopts common best practices for Python projects, emphasizing a clear separation of concerns.

```mermaid
graph TD
    A[.]
    A --> B1["/.gitignore"]
    A --> B2["/pyproject.toml"]
    A --> B3["/README.md"]
    A --> C1["src/"]
    C1 --> D1["hsi_pipeline/"]
    D1 --> E1["__init__.py"]
    D1 --> E2["main_pipeline.py"]
    D1 --> E3["georeference_hsi_pixels.py"]
    D1 --> E4["lever_arm_utils.py"]
    D1 --> E5["logging_config.py"]
    D1 --> E6["pipeline_exceptions.py"]
    D1 --> E7["synchronize_hsi_webodm.py"]
    D1 --> E8["plotting/"]
    E8 --> F1["plot_hsi_data.py"]
    A --> C2["tests/"]
    C2 --> D2["__init__.py"]
    C2 --> D3["test_main_pipeline.py"]
    C2 --> D4["test_georeferencing.py"]
    C2 --> D5["test_lever_arm.py"]
    C2 --> D6["test_logging_config.py"]
    C2 --> D7["test_consolidation.py"]
    A --> C3["data/"]
    C3 --> D8["WebODM/"]
    A --> C4["docs/"]
    C4 --> D9["main_pipeline_documentation.md"]
    C4 --> D10["TDD.md"]
    C4 --> D11["images/"]
    A --> C5["output/"]
    C5 --> D12["results/"]
    D12 --> E9["georeferenced_pixels.csv"]
    D12 --> E10["hsi_poses.csv"]
    C5 --> D13["plots/"]
    D13 --> E11["plot_interpolation_quality.png"]
    C5 --> D14["logs/"]
    D14 --> E12["pipeline.log"]
    A --> C6["research/"]
    A --> C7["aigi_workflow/"]
    C7 --> D15["LS1/"]
    D15 --> E13["prompts.md"]
    D15 --> E14["reflection.md"]
    D15 --> E15["responses.md"]
    D15 --> E16["scores.json"]
    D15 --> E17["test_specs.md"]
    C7 --> D16["LS2/"]
    C7 --> D17["..."]
    A --> C8["scripts/"]
    A --> C9["venv/"]
    A --> C10["htmlcov/"]
    A --> C11["dg_mvp.egg-info/"]
```

**Detailed Structure:**

*   `.gitignore`: Updated to reflect new ignored directories and common Python ignores.
*   `pyproject.toml`: Stays at the root (standard for Python packaging and configuration).
*   `README.md`: (Recommended) A central place for project overview, setup, and usage instructions.
*   `src/`: Contains the main source code for the pipeline.
    *   `hsi_pipeline/`: The primary Python package for the project.
        *   `__init__.py`: Makes `hsi_pipeline` a package.
        *   `main_pipeline.py`: Main entry point or core logic.
        *   Other `.py` files: Core modules of the pipeline.
        *   `plotting/`: Sub-module for plotting utilities if they are integral to the library.
            *   `plot_hsi_data.py`
*   `tests/`: Contains all test scripts.
    *   `__init__.py`: Makes `tests` a package (optional, but good practice).
    *   `test_*.py`: Individual test files corresponding to modules in `src/`.
*   `data/`: (Existing) For raw input data, intermediate data files that are inputs to later stages.
*   `docs/`: For all project documentation.
    *   `main_pipeline_documentation.md`, `TDD.md`, etc.
    *   `images/`: Optional, for images used within the documentation.
*   `output/`: (Existing, but reorganized) For all files generated by the pipeline.
    *   `results/`: For final data outputs like CSV files.
    *   `plots/`: For generated plots (current `plots/` directory contents move here).
    *   `logs/`: For log files.
*   `research/`: (Existing) For research notes, literature, and related documents.
*   `aigi_workflow/`: Dedicated directory for aiGI artifacts, organized by Learning Step (LS).
    *   `LS1/`, `LS2/`, ... `LSN/`: Each subdirectory contains all artifacts for that iteration.
        *   `prompts.md`
        *   `reflection.md`
        *   `responses.md`
        *   `scores.json`
        *   `test_specs.md`
*   `scripts/`: (Optional) For standalone utility scripts that are not part of the main importable package (e.g., data conversion scripts, batch processing helpers). If `plot_hsi_data.py` is more of a standalone analysis script than a library component, it could go here.
*   `venv/` (or `.venv`, `env`): Virtual environment directory (should be in `.gitignore`).
*   `htmlcov/`: Test coverage reports (should be in `.gitignore`).
*   `dg_mvp.egg-info/`: Python package build artifacts (should be in `.gitignore`).

### 3. Detailed File Migration Plan

This section details where existing files from the root (and other locations) should be moved.

| Current Location (Root unless specified) | Proposed New Location                     | Notes                                                                 |
| :--------------------------------------- | :---------------------------------------- | :-------------------------------------------------------------------- |
| `georeference_hsi_pixels.py`             | `src/hsi_pipeline/georeference_hsi_pixels.py` | Core source file                                                      |
| `georeferenced_pixels.csv`               | `output/results/georeferenced_pixels.csv` | Generated output data                                                 |
| `hsi_poses.csv`                          | `output/results/hsi_poses.csv`            | Generated output data                                                 |
| `lever_arm_utils.py`                     | `src/hsi_pipeline/lever_arm_utils.py`     | Core source file                                                      |
| `logging_config.py`                      | `src/hsi_pipeline/logging_config.py`      | Core source file                                                      |
| `main_pipeline_documentation.md`         | `docs/main_pipeline_documentation.md`     | Documentation                                                         |
| `main_pipeline.py`                       | `src/hsi_pipeline/main_pipeline.py`       | Core source file                                                      |
| `pipeline_exceptions.py`                 | `src/hsi_pipeline/pipeline_exceptions.py` | Core source file                                                      |
| `pipeline.log`                           | `output/logs/pipeline.log`                | Log file                                                              |
| `plot_hsi_data.py`                       | `src/hsi_pipeline/plotting/plot_hsi_data.py` | Or `scripts/plot_hsi_data.py` if standalone                       |
| `prompts_LS*.md`                         | `aigi_workflow/LS*/prompts.md`            | aiGI artifact (e.g., `prompts_LS1.md` -> `aigi_workflow/LS1/prompts.md`) |
| `reflection_LS*.md`                      | `aigi_workflow/LS*/reflection.md`         | aiGI artifact                                                         |
| `responses_LS*.md`                       | `aigi_workflow/LS*/responses.md`          | aiGI artifact                                                         |
| `scores_LS*.json`                        | `aigi_workflow/LS*/scores.json`           | aiGI artifact                                                         |
| `synchronize_hsi_webodm.py`              | `src/hsi_pipeline/synchronize_hsi_webodm.py`| Core source file                                                      |
| `TDD.md`                                 | `docs/TDD.md`                             | Documentation                                                         |
| `test_consolidation.py`                  | `tests/test_consolidation.py`             | Test script                                                           |
| `test_georeferencing.py`                 | `tests/test_georeferencing.py`            | Test script                                                           |
| `test_lever_arm.py`                      | `tests/test_lever_arm.py`                 | Test script                                                           |
| `test_logging_config.py`                 | `tests/test_logging_config.py`            | Test script                                                           |
| `test_main_pipeline.py`                  | `tests/test_main_pipeline.py`             | Test script                                                           |
| `test_specs_LS*.md`                      | `aigi_workflow/LS*/test_specs.md`         | aiGI artifact                                                         |
| `plots/*` (e.g. `plots/plot_interpolation_quality.png`) | `output/plots/*`           | Move existing plots to the new consolidated output directory        |

**Existing Directories:**
*   `data/`: Remains as is.
*   `research/`: Remains as is.
*   `htmlcov/`: Remains at root (or move to `build/coverage/`), add to `.gitignore`.
*   `dg_mvp.egg-info/`: Remains at root, add to `.gitignore`.
*   `output/`: Will be the parent for `results/`, `plots/`, `logs/`. Any other current content in `output/` should be assessed and moved to appropriate subdirectories.

### 4. Rationale for Changes

*   **Clarity and Organization**: The primary benefit is a much clearer separation of concerns. Developers can immediately understand where to find source code, tests, documentation, and generated outputs.
*   **Reduced Root Clutter**: A cleaner project root makes it easier to navigate and identify key configuration files (`pyproject.toml`, `README.md`).
*   **Maintainability**: A well-defined structure simplifies maintenance, onboarding of new team members, and debugging.
*   **Scalability**: As the project grows, this structure can accommodate new modules, tests, and documents more gracefully.
*   **Standardization**: Adheres to common Python project layout conventions, making it familiar to Python developers and compatible with standard build tools.
*   **aiGI Artifact Management**: The `aigi_workflow/LS*/` structure provides a clean, chronological, and grouped organization for the numerous aiGI artifacts, making it easy to track the evolution of each learning step.
*   **Output Management**: Consolidating all generated files under `output/` (with subdirectories for `logs`, `plots`, `results`) keeps transient or reproducible files separate from source code and data.

### 5. Handling of Generated Files

*   **Logs (`pipeline.log`)**: Moved to `output/logs/`. These are typically not version-controlled.
*   **CSV Outputs (`georeferenced_pixels.csv`, `hsi_poses.csv`)**: Moved to `output/results/`. Whether these are version-controlled depends on project needs. If they are large or frequently regenerated, they might be gitignored. If they represent specific experimental results to be tracked, they might be versioned (or stored elsewhere, e.g., DVC, Git LFS).
*   **Plots (existing `plots/` directory)**: Moved to `output/plots/`. Similar to CSVs, version control depends on their nature.
*   **Coverage Reports (`htmlcov/`)**: This is a build artifact and should be gitignored. It can remain at the root or be moved to a general `build/` directory (e.g., `build/coverage/`).
*   **Python Build Artifacts (`dg_mvp.egg-info/`, `__pycache__/`, etc.)**: These should always be gitignored.

### 6. Suggested `.gitignore` Additions

A comprehensive `.gitignore` is crucial. Here are suggested additions/rules:

```gitignore
# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# C extensions
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
pip-wheel-metadata/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
# Usually these files are written by a PyInstaller script; this is deployed to PyPI.
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/
.pytest_cache/

# Translations
*.mo
*.pot

# Django stuff:
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal

# Flask stuff:
instance/
.webassets-cache

# Scrapy stuff:
.scrapy

# Sphinx documentation
docs/_build/

# PyBuilder
target/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# PEP 582; __pypackages__ directory
__pypackages__/

# Celery stuff
celerybeat-schedule
celerybeat.pid

# SageMath parsed files
*.sage.py

# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# Output files from this project
output/logs/
# Consider adding if these are always regenerated and not meant for version control:
# output/results/
# output/plots/

# VS Code
.vscode/

# Operating System files
.DS_Store
Thumbs.db
```

### 7. Conclusion

Adopting this revised folder structure will significantly improve the HSI Georeferencing Pipeline project's organization, making it more robust, maintainable, and easier to navigate for all contributors. It aligns with standard practices and provides a scalable foundation for future development.

---

I recommend creating or updating a `README.md` file at the project root to explain the new structure, setup instructions, and how to run the pipeline and tests.