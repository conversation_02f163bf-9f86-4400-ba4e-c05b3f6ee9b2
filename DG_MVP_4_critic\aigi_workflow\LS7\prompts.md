## Prompt [LS7_1]

### Context
Based on [`reflection_LS6.md#Issue-1`](reflection_LS6.md:14-26), there's unverified test coverage for [`vectorized_georef.py`](vectorized_georef.py). While an 89% coverage is claimed with 8 new tests, specific unit tests for all public helper functions are not visible. This verification is crucial for ensuring correctness and maintainability. The overall project coverage is 37%, significantly below the 50% target ([`scores_LS6.json`](scores_LS6.json:23)).

### Objective
Ensure comprehensive test coverage for all public helper functions in [`vectorized_georef.py`](vectorized_georef.py) and verify the 89% coverage claim for this module.

### Focus Areas
-   Implementation of dedicated unit tests for public helper functions in [`vectorized_georef.py`](vectorized_georef.py).
-   Verification of the 89% test coverage for [`vectorized_georef.py`](vectorized_georef.py).

### Code Reference
File: [`vectorized_georef.py`](vectorized_georef.py)
Functions to ensure are tested:
-   [`calculate_sensor_view_vectors_vectorized`](vectorized_georef.py:171-215)
-   [`transform_to_world_coordinates_vectorized`](vectorized_georef.py:218-236)
-   [`calculate_flat_plane_intersections_vectorized`](vectorized_georef.py:239-286)

Test file: [`test_georeferencing.py`](test_georeferencing.py) (or a dedicated `test_vectorized_georef.py`)

### Requirements
1.  Review `test_specs_LS6.md` (Test Cases 2.1-2.7) and ensure all 8 new tests for [`vectorized_georef.py`](vectorized_georef.py) are present and correctly implemented.
2.  Add new, dedicated unit tests for the public helper functions listed above if not already comprehensively covered. These tests must cover valid inputs, edge cases, and error conditions for each function.
3.  After implementing/verifying tests, use a coverage tool to confirm that [`vectorized_georef.py`](vectorized_georef.py) achieves at least 89% line coverage.
4.  Ensure tests are granular and test each public helper function independently.

### Expected Improvements
-   Increased confidence in the correctness of [`vectorized_georef.py`](vectorized_georef.py) helper functions.
-   Verified line coverage of at least 89% for [`vectorized_georef.py`](vectorized_georef.py).
-   Contribution towards the overall project test coverage target of 50%.
-   Improved maintainability of [`vectorized_georef.py`](vectorized_georef.py).

---

## Prompt [LS7_2]

### Context
The overall performance score is 67, below the target of 75 ([`scores_LS6.json`](scores_LS6.json:8)). [`reflection_LS6.md#Optimization-Opportunities`](reflection_LS6.md:113) identifies "Vectorized DSM Intersection" as the most significant optimization opportunity. The current `process_hsi_line_vectorized` function in [`vectorized_georef.py`](vectorized_georef.py:388-401) still uses per-pixel processing for DSM intersections, impacting scalability and resource usage.

### Objective
Optimize the georeferencing process by implementing fully vectorized DSM intersection within the `process_hsi_line_vectorized` function in [`vectorized_georef.py`](vectorized_georef.py).

### Focus Areas
-   Modification of `process_hsi_line_vectorized` in [`vectorized_georef.py`](vectorized_georef.py).
-   Efficient, vectorized calculation of ray intersections with the DSM.
-   Maintaining or improving the accuracy of georeferenced points.

### Code Reference
File: [`vectorized_georef.py`](vectorized_georef.py)
Function: `process_hsi_line_vectorized` (specifically the DSM intersection part, currently per-pixel, around lines [`vectorized_georef.py:388-401`](vectorized_georef.py:388-401) as per reflection context).

### Requirements
1.  Refactor the DSM intersection logic within `process_hsi_line_vectorized` to operate on all sensor view vectors for a given HSI line simultaneously (vectorized approach).
2.  Avoid per-pixel iteration for DSM sampling or intersection calculations.
3.  Ensure the new implementation correctly handles edge cases, such as rays not intersecting the DSM or areas outside DSM coverage.
4.  Update or add unit tests in [`test_georeferencing.py`](test_georeferencing.py) to specifically validate the vectorized DSM intersection logic, including performance benchmarks if feasible.
5.  The solution should aim to improve `resource_usage_score` (currently 65) and `scalability_score` (currently 60).

### Expected Improvements
-   Significant improvement in the `performance` aggregate score, aiming for the target of 75.
-   Improved `resource_usage_score` and `scalability_score`.
-   Reduced processing time for georeferencing when using the DSM method.
-   More efficient use of computational resources (CPU, memory).

---

## Prompt [LS7_3]

### Context
The overall project line coverage is 37% ([`scores_LS6.json`](scores_LS6.json:40)), which is below the target of 50% ([`scores_LS6.json`](scores_LS6.json:23)). While [`georeference_hsi_pixels.py`](georeference_hsi_pixels.py) (86%) and [`vectorized_georef.py`](vectorized_georef.py) (target 89%) have high or targeted high coverage, other modules are likely contributing to the low overall figure.

### Objective
Increase overall project test coverage to at least 50% by adding unit tests to modules with currently low or missing coverage.

### Focus Areas
-   Identification of Python modules (other than `georeference_hsi_pixels.py` and `vectorized_georef.py`) with low test coverage.
-   Creation and implementation of new unit tests for these identified modules.

### Code Reference
Potential files needing increased test coverage (based on file list and absence of dedicated test files or known low coverage):
-   [`synchronize_hsi_webodm.py`](synchronize_hsi_webodm.py)
-   [`compare_timestamps.py`](compare_timestamps.py)
-   [`create_georeferenced_rgb.py`](create_georeferenced_rgb.py)
-   [`lever_arm_utils.py`](lever_arm_utils.py) (check coverage of [`test_lever_arm.py`](test_lever_arm.py))
-   [`main_pipeline.py`](main_pipeline.py) (check coverage of [`test_main_pipeline.py`](test_main_pipeline.py))
-   [`create_consolidated_webodm_poses.py`](create_consolidated_webodm_poses.py) (check coverage of [`test_consolidation.py`](test_consolidation.py))
-   [`pipeline_exceptions.py`](pipeline_exceptions.py)
-   [`logging_config.py`](logging_config.py)
-   [`plot_hsi_data.py`](plot_hsi_data.py) (if core logic is testable)

### Requirements
1.  Analyze the project structure and existing tests to identify modules with the lowest test coverage or no tests.
2.  Prioritize modules based on their criticality and complexity.
3.  For each prioritized module, write comprehensive unit tests covering its public functions/classes, including valid inputs, edge cases, and error handling.
4.  Follow the Arrange-Act-Assert (AAA) pattern for new tests.
5.  Ensure new tests are independent and can run in any order.
6.  Aim to increase the `overall_line_coverage_reported_estimate` to at least 50%.

### Expected Improvements
-   Overall project line coverage increased to at least 50%.
-   Improved robustness and reliability of the newly tested modules.
-   Easier maintenance and refactoring of the codebase due to better test safety nets.

---

## Prompt [LS7_4]

### Context
[`reflection_LS6.md#Issue-5`](reflection_LS6.md:85-95) highlights a persistent issue in [`georeference_hsi_pixels.py`](georeference_hsi_pixels.py) where an unknown `z_ground_calculation_method` results in a warning and fallback to a default value, rather than a more explicit error. This can mask configuration issues.

### Objective
Modify the `run_georeferencing` function in [`georeference_hsi_pixels.py`](georeference_hsi_pixels.py) to raise a `PipelineConfigError` when an unrecognized `z_ground_calculation_method` is provided in the configuration.

### Focus Areas
-   Error handling for `z_ground_calculation_method` in [`georeference_hsi_pixels.py`](georeference_hsi_pixels.py).
-   Ensuring robust configuration validation.

### Code Reference
File: [`georeference_hsi_pixels.py`](georeference_hsi_pixels.py)
Problematic section (around lines [`georeference_hsi_pixels.py:600-602`](georeference_hsi_pixels.py:600-602)):
```python
# In georeference_hsi_pixels.py, run_georeferencing function
# ...
    elif z_ground_method == "dsm_intersection":
        # ...
    else: # Unknown method
        logger.warning(f"Unknown z_ground_calculation_method: '{z_ground_method}'. Using fallback for flat plane")
        Z_ground_flat_plane = default_fallback_z_ground
```

### Requirements
1.  Change the `else` block in the `z_ground_calculation_method` conditional logic.
2.  Instead of logging a warning and using `default_fallback_z_ground`, raise a `PipelineConfigError` (ensure this custom exception is available or define it in [`pipeline_exceptions.py`](pipeline_exceptions.py) if it doesn't exist).
3.  The error message should clearly state the unknown method provided and list the valid methods (e.g., "avg_pose_z_minus_offset", "fixed_value", "dsm_intersection").
4.  Add or update unit tests in [`test_georeferencing.py`](test_georeferencing.py) to verify that `PipelineConfigError` is raised correctly for invalid `z_ground_calculation_method` values.

### Expected Improvements
-   Improved configuration validation and error reporting.
-   Prevention of silent failures or unexpected behavior due to misconfigured `z_ground_calculation_method`.
-   Increased `correctness` score related to error handling.

---

## Prompt [LS7_5]

### Context
[`reflection_LS6.md#Issue-2`](reflection_LS6.md:27-32) notes a missing test case in [`test_georeferencing.py`](test_georeferencing.py). Specifically, `test_specs_LS6.md` Test Case 3.3 ([`test_specs_LS6.md:579-611`](test_specs_LS6.md:579-611)) for DSM path resolution, where a relative DSM path resolves correctly but the target file doesn't exist, is not implemented. The test should be named `test_dsm_path_resolution_relative_file_not_found_at_resolved_path`.

### Objective
Implement the missing test case `TestLS6DSMPathResolution.test_dsm_path_resolution_relative_file_not_found_at_resolved_path` in [`test_georeferencing.py`](test_georeferencing.py) as specified.

### Focus Areas
-   Completing test coverage for DSM path resolution logic in [`georeference_hsi_pixels.py`](georeference_hsi_pixels.py).
-   Ensuring correct error handling for non-existent DSM files after path resolution.

### Code Reference
Test file: [`test_georeferencing.py`](test_georeferencing.py)
Test Class: `TestLS6DSMPathResolution`
Relevant function in main code: `get_dsm_handler` or similar in [`georeference_hsi_pixels.py`](georeference_hsi_pixels.py) (around [`georeference_hsi_pixels.py:465-477`](georeference_hsi_pixels.py:465-477)).

### Requirements
1.  Add the test method `test_dsm_path_resolution_relative_file_not_found_at_resolved_path` to the `TestLS6DSMPathResolution` class in [`test_georeferencing.py`](test_georeferencing.py).
2.  The test should mock `Path.exists()` for the resolved path to return `False`.
3.  The test should set up a configuration where `dsm_path` is relative and `config_file_path` is provided.
4.  Assert that the appropriate error (e.g., `FileNotFoundError` or `rasterio.errors.RasterioIOError`) is raised when the DSM file is attempted to be accessed/opened by the georeferencing logic.
5.  Follow the specifications outlined in [`test_specs_LS6.md:588-611`](test_specs_LS6.md:588-611).

### Expected Improvements
-   More robust testing of the DSM path resolution logic.
-   Confirmation of correct error handling for a specific edge case.
-   Slight increase in test coverage for [`georeference_hsi_pixels.py`](georeference_hsi_pixels.py).

---

## Prompt [LS7_6]

### Context
[`reflection_LS6.md#Issue-3`](reflection_LS6.md:33-44) suggests potential redundancy or unclear distinction between internal (`_` prefixed) helper functions and their public counterparts in [`vectorized_georef.py`](vectorized_georef.py). This can affect maintainability and code clarity.

### Objective
Review the internal helper functions in [`vectorized_georef.py`](vectorized_georef.py) for redundancy against their public counterparts. Either remove them if redundant or clarify their distinct purpose with documentation and specific tests.

### Focus Areas
-   Code clarity and maintainability of [`vectorized_georef.py`](vectorized_georef.py).
-   Elimination of redundant code.

### Code Reference
File: [`vectorized_georef.py`](vectorized_georef.py)
Potentially redundant pairs:
-   `_calculate_sensor_pixel_vectors_vectorized` vs. `calculate_sensor_view_vectors_vectorized`
-   `_transform_vectors_to_world_vectorized` vs. `transform_to_world_coordinates_vectorized`
-   `_intersect_rays_with_horizontal_plane_vectorized` vs. `calculate_flat_plane_intersections_vectorized`

### Requirements
1.  Analyze the usage and implementation of the listed internal (`_` prefixed) helper functions in [`vectorized_georef.py`](vectorized_georef.py).
2.  Compare them with their respective public counterparts.
3.  If an internal helper is fully superseded by its public version or is unused in any active code path, remove it and its associated tests (if those tests are also redundant).
4.  If an internal helper serves a distinct, necessary purpose (e.g., handles specific edge cases not covered by the public function, or has different input/output assumptions), clearly document this purpose with comments within the code.
5.  Ensure that if retained, these internal functions have specific unit tests that demonstrate their unique utility and are not just duplicates of tests for public functions.

### Expected Improvements
-   Streamlined and more maintainable [`vectorized_georef.py`](vectorized_georef.py) module.
-   Reduced code duplication if redundancies are found and removed.
-   Improved clarity regarding the purpose of each helper function.
-   Slight improvement in `complexity` or `maintainability_index_score`.

---

## Prompt [LS7_7]

### Context
[`reflection_LS6.md#Issue-4`](reflection_LS6.md:45-57) points out that the logger initialization in [`main_pipeline.py`](main_pipeline.py) is done within functions (`load_pipeline_config`, `run_complete_pipeline`) rather than once at the module level. This is a minor style issue but deviates from common practice.

### Objective
Refactor [`main_pipeline.py`](main_pipeline.py) to initialize the logger once at the module level.

### Focus Areas
-   Adherence to Python logging best practices.
-   Code style and consistency in [`main_pipeline.py`](main_pipeline.py).

### Code Reference
File: [`main_pipeline.py`](main_pipeline.py)
Problematic lines:
-   [`main_pipeline.py:49`](main_pipeline.py:49) (in `load_pipeline_config`): `logger = get_logger(__name__)`
-   [`main_pipeline.py:81`](main_pipeline.py:81) (in `run_complete_pipeline`): `logger = get_logger(__name__)`

Recommended Fix (from reflection):
```python
# main_pipeline.py
import toml
import logging # Keep this
from pathlib import Path
from typing import Dict, Any

from logging_config import setup_logging, get_logger

# Initialize logger at module level
logger = get_logger(__name__) # ADD THIS

# ... rest of imports and code ...

def load_pipeline_config(config_path: str) -> Dict[str, Any]:
    # logger = get_logger(__name__) # REMOVE THIS LINE
    # Use the module-level logger directly
    # ...
    return config # ensure return is present

def run_complete_pipeline(config_path: str = 'config.toml') -> bool:
    setup_logging(log_level="INFO", log_file="pipeline.log") # This sets up the logger
    # logger = get_logger(__name__) # REMOVE THIS
    # ... use module-level logger ...
```

### Requirements
1.  Add `logger = get_logger(__name__)` at the module level (top of the file after imports) in [`main_pipeline.py`](main_pipeline.py).
2.  Remove the redundant `logger = get_logger(__name__)` calls from within the `load_pipeline_config` and `run_complete_pipeline` functions.
3.  Ensure that `setup_logging()` is still called appropriately (e.g., in `run_complete_pipeline` or at application entry point) to configure the root logger and handlers. The module-level `get_logger` will then use this configuration.
4.  Verify that logging continues to function as expected after the change.

### Expected Improvements
-   Improved code style in [`main_pipeline.py`](main_pipeline.py).
-   Adherence to standard Python logging practices.
-   Minor simplification of function bodies.