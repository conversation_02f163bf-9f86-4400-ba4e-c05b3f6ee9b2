# Dokumentation: `main_pipeline.py`

Das Skript [`main_pipeline.py`](main_pipeline.py:1) orchestriert eine vollständige Pipeline zur direkten Georeferenzierung von Hyperspektralbilddaten (HSI). Es führt eine Reihe von Verarbeitungsschritten sequenziell aus, die jeweils von einer Konfigurationsdatei (`config.toml` per Standard) gesteuert werden.

## Übersicht der Pipeline

Die Hauptfunktion [`run_complete_pipeline(config_path: str = 'config.toml')`](main_pipeline.py:9) führt die folgenden Schritte aus:

1.  **Konsolidierung der WebODM Posen**: Verarbeitung und Vereinheitlichung von Posen-Daten, die von WebODM stammen.
2.  **HSI Posen Synchronisation**: Synchronisation der HSI-Posen mit einer Referenzzeitquelle oder anderen Sensordaten.
3.  **Direkte Georeferenzierung der HSI Pixel**: Berechnung der geographischen Koordinaten für jeden HSI-Pixel.
4.  **Erstellung des georeferenzierten RGB GeoTIFFs**: Erzeugung eines georeferenzierten RGB-Bildes im GeoTIFF-Format.
5.  **Erstellung der Plots**: Generierung von Visualisierungen zur Qualitätskontrolle und Analyse.

Jeder Schritt ist modular aufgebaut und wird durch eine eigene Funktion im Skript repräsentiert. Die Pipeline bricht ab, wenn einer der ersten drei kritischen Schritte fehlschlägt. Fehler in den Schritten 4 und 5 führen zu Warnungen, aber die Pipeline wird fortgesetzt, da die Ergebnisse dieser Schritte als optional betrachtet werden können.

## Detaillierte Beschreibung der Verarbeitungsschritte

### 1. Konsolidierung der WebODM Posen

-   **Funktion im Skript**: [`run_consolidation(config_path)`](main_pipeline.py:24) (aufgerufen aus [`create_consolidated_webodm_poses.py`](create_consolidated_webodm_poses.py:1))
-   **Beschreibung**: Dieser Schritt verarbeitet und konsolidiert Poseninformationen (Position und Orientierung) von WebODM. Er liest die Posen aus einer `shots.geojson`-Datei und korreliert sie mit Zeitstempeln aus `.haip`-Dateien, die zu den RGB-Bildern gehören.
-   **Eingabe (gemäß [`config.toml`](config.toml:1)):**
    -   WebODM Posen-Datei: [`data/WebODM/shots.geojson`](data/WebODM/shots.geojson:1) (aus `paths.webodm_data_directory` + `paths.shots_geojson_file`)
    -   Verzeichnis mit HAIP-Dateien: [`data/WebODM/haip_files/`](data/WebODM/haip_files/:1) (aus `paths.webodm_data_directory` + `paths.haip_files_subdirectory`)
    -   Schlüssel für Zeitstempel in HAIP-Dateien: `"rgb"` (aus `parameters.webodm_consolidation.haip_timestamp_key`)
-   **Ausgabe (gemäß [`config.toml`](config.toml:1)):**
    -   Konsolidierte Posen als CSV-Datei: [`webodm_poses_consolidated.csv`](webodm_poses_consolidated.csv:1) (gespeichert in `paths.output_directory`, Dateiname aus `paths.consolidated_webodm_poses_csv`)
    -   Ein boolescher Wert, der den Erfolg des Schritts anzeigt.
-   **Beispielhafter Verarbeitungsablauf:**
    Das Skript liest Kamerakoordinaten und -orientierungen aus [`data/WebODM/shots.geojson`](data/WebODM/shots.geojson:1). Für jeden Eintrag (Shot), der einem Bild entspricht, wird die zugehörige `.haip`-Datei aus dem Verzeichnis [`data/WebODM/haip_files/`](data/WebODM/haip_files/:1) gesucht. Aus dieser `.haip`-Datei wird der Zeitstempel unter dem Schlüssel `"rgb"` extrahiert. Diese kombinierten Informationen (Pose und Zeitstempel) werden aufbereitet und in der CSV-Datei [`webodm_poses_consolidated.csv`](webodm_poses_consolidated.csv:1) gespeichert.

### 2. HSI Posen Synchronisation

-   **Funktion im Skript**: [`run_synchronization(config_path)`](main_pipeline.py:30) (aufgerufen aus [`synchronize_hsi_webodm.py`](synchronize_hsi_webodm.py:1))
-   **Beschreibung**: Synchronisiert die Zeitstempel der HSI-Aufnahmen (Scanlines) mit den in Schritt 1 konsolidierten und zeitlich geordneten WebODM-Kamera-Posen. Dies ermöglicht die Interpolation der Kamerapose für jeden HSI-Zeitstempel.
-   **Eingabe (gemäß [`config.toml`](config.toml:1)):**
    -   HSI-Synchronisationsdatei (Zeitstempel der Scanlines): [`data/HSI/2025-05-15_08-28-48_cont.sync.txt`](data/HSI/2025-05-15_08-28-48_cont.sync.txt:1) (aus `paths.hsi_data_directory` + `paths.hsi_base_filename` + `.sync.txt`)
    -   Konsolidierte WebODM Posen-Datei: [`webodm_poses_consolidated.csv`](webodm_poses_consolidated.csv:1) (Ausgabe von Schritt 1)
-   **Ausgabe (gemäß [`config.toml`](config.toml:1)):**
    -   Synchronisierte HSI-Posen als CSV-Datei: [`hsi_poses.csv`](hsi_poses.csv:1) (gespeichert in `paths.output_directory`, Dateiname aus `paths.hsi_poses_csv`)
    -   Ein boolescher Wert, der den Erfolg des Schritts anzeigt.
-   **Beispielhafter Verarbeitungsablauf:**
    Das Skript liest die Zeitstempel jeder HSI-Scanline aus [`data/HSI/2025-05-15_08-28-48_cont.sync.txt`](data/HSI/2025-05-15_08-28-48_cont.sync.txt:1). Es verwendet dann die zeitlich geordneten Kameraposen aus [`webodm_poses_consolidated.csv`](webodm_poses_consolidated.csv:1), um für jeden HSI-Zeitstempel die exakte Kameraposition (X, Y, Z) und -orientierung (Omega, Phi, Kappa) durch Interpolation zu bestimmen. Das Ergebnis wird in [`hsi_poses.csv`](hsi_poses.csv:1) gespeichert, wobei jede Zeile einer HSI-Scanline eine zugeordnete, interpolierte Pose enthält.

### 3. Direkte Georeferenzierung der HSI Pixel

-   **Funktion im Skript**: [`run_georeferencing(config_path)`](main_pipeline.py:36) (aufgerufen aus [`georeference_hsi_pixels.py`](georeference_hsi_pixels.py:1))
-   **Beschreibung**: Berechnet für jeden HSI-Pixel dessen geographische Koordinaten (X, Y, Z) auf dem Erdboden. Dies geschieht durch Kombination der interpolierten Kamerapose (aus Schritt 2), des HSI-Sensormodells, Boresight-Korrekturen und der Schnittpunktberechnung eines Sichtstrahls mit einem Digitalen Oberflächenmodell (DSM).
-   **Eingabe (gemäß [`config.toml`](config.toml:1)):**
    -   HSI-Bilddaten: [`data/HSI/2025-05-15_08-28-48_cont.img`](data/HSI/2025-05-15_08-28-48_cont.img:1) und zugehörige [`data/HSI/2025-05-15_08-28-48_cont.hdr`](data/HSI/2025-05-15_08-28-48_cont.hdr:1) (aus `paths.hsi_data_directory` + `paths.hsi_base_filename`)
    -   Synchronisierte HSI-Posen: [`hsi_poses.csv`](hsi_poses.csv:1) (Ausgabe von Schritt 2)
    -   Sensormodell-Datei: [`data/HSI/Sensormodel_HAIP_BlackBirdV2_No6_20m.txt`](data/HSI/Sensormodel_HAIP_BlackBirdV2_No6_20m.txt:1) (aus `paths.hsi_data_directory` + `paths.sensor_model_file`)
    -   Digitales Oberflächenmodell (DSM): [`data/WebODM/dsm.tif`](data/WebODM/dsm.tif:1) (aus `paths.dsm_file`)
    -   Boresight-Korrekturwinkel (Roll, Pitch, Yaw): `0.1`, `-1.35`, `0.0` Grad (aus `parameters.georeferencing`)
    -   Hebelarm-Komponenten (X, Y, Z): `0.0`, `0.0`, `0.0` Meter (aus `parameters.georeferencing`)
    -   Methode zur Höhenbestimmung: `"dsm_intersection"` (aus `parameters.georeferencing.z_ground_calculation_method`)
    -   Parameter für Ray-DSM-Intersection: `max_search_dist_m = 2000.0`, `ray_dsm_step_m = 5.0`, `ray_dsm_bisection_tolerance_m = 0.01` (aus `parameters.georeferencing`)
-   **Ausgabe (gemäß [`config.toml`](config.toml:1)):**
    -   Georeferenzierte HSI-Pixelkoordinaten als CSV: [`georeferenced_pixels.csv`](georeferenced_pixels.csv:1) (gespeichert in `paths.output_directory`, Dateiname aus `paths.georeferenced_pixels_csv`)
    -   Ein boolescher Wert, der den Erfolg des Schritts anzeigt.
-   **Beispielhafter Verarbeitungsablauf:**
    Für jeden Pixel in jeder Scanline der HSI-Daten (aus [`data/HSI/2025-05-15_08-28-48_cont.img`](data/HSI/2025-05-15_08-28-48_cont.img:1)) wird dessen Blickrichtung relativ zum Sensor mithilfe des Sensormodells ([`data/HSI/Sensormodel_HAIP_BlackBirdV2_No6_20m.txt`](data/HSI/Sensormodel_HAIP_BlackBirdV2_No6_20m.txt:1)) bestimmt. Diese Blickrichtung wird mit der interpolierten und Boresight-korrigierten (Roll: 0.1°, Pitch: -1.35°, Yaw: 0°) Kamerapose aus [`hsi_poses.csv`](hsi_poses.csv:1) kombiniert, um einen Sichtstrahl vom Sensor zum Boden zu definieren. Der Schnittpunkt dieses Strahls mit dem DSM ([`data/WebODM/dsm.tif`](data/WebODM/dsm.tif:1)) wird berechnet (maximale Suchdistanz 2000m, Schrittweite 5m, Toleranz 0.01m). Die resultierenden X, Y, Z Weltkoordinaten für jeden HSI-Pixel werden in [`georeferenced_pixels.csv`](georeferenced_pixels.csv:1) gespeichert.

### 4. Erstellung des georeferenzierten RGB GeoTIFFs

-   **Funktion im Skript**: [`run_create_rgb_geotiff(config_path)`](main_pipeline.py:42) (aufgerufen aus [`create_georeferenced_rgb.py`](create_georeferenced_rgb.py:1))
-   **Beschreibung**: Erstellt aus den HSI-Daten und den in Schritt 3 berechneten georeferenzierten Pixelkoordinaten ein georeferenziertes RGB-Bild im GeoTIFF-Format. Es werden spezifische Wellenlängen für die R-, G- und B-Kanäle ausgewählt und das Bild auf eine Zielauflösung resampelt.
-   **Eingabe (gemäß [`config.toml`](config.toml:1)):**
    -   HSI-Bilddaten: [`data/HSI/2025-05-15_08-28-48_cont.img`](data/HSI/2025-05-15_08-28-48_cont.img:1) und [`data/HSI/2025-05-15_08-28-48_cont.hdr`](data/HSI/2025-05-15_08-28-48_cont.hdr:1)
    -   Georeferenzierte HSI-Pixelkoordinaten: [`georeferenced_pixels.csv`](georeferenced_pixels.csv:1) (Ausgabe von Schritt 3)
    -   Ziel-Wellenlängen für R, G, B: `800.0` nm, `700.0` nm, `550.0` nm (aus `parameters.rgb_geotiff_creation`)
    -   Ziel-Auflösung: `0.1` Meter (aus `parameters.rgb_geotiff_creation.target_resolution_meters`)
    -   Ausgabe EPSG-Code: `32632` (UTM Zone 32N, WGS84) (aus `parameters.rgb_geotiff_creation.output_epsg_code`)
    -   Normalisierungsmethode: `"min_max"` (aus `parameters.rgb_geotiff_creation.normalization_method`)
-   **Ausgabe (gemäß [`config.toml`](config.toml:1)):**
    -   Georeferenziertes RGB GeoTIFF: [`georeferenced_rgb_via_config.tif`](georeferenced_rgb_via_config.tif:1) (gespeichert in `paths.output_directory`, Dateiname aus `paths.georeferenced_rgb_tif`)
    -   Ein boolescher Wert, der den Erfolg des Schritts anzeigt. (Ein Fehlschlag hier führt nur zu einer Warnung).
-   **Beispielhafter Verarbeitungsablauf:**
    Das Skript wählt aus dem HSI-Datenkubus ([`data/HSI/2025-05-15_08-28-48_cont.img`](data/HSI/2025-05-15_08-28-48_cont.img:1)) die Spektralbänder aus, die den Zielwellenlängen am nächsten kommen (R: 800nm, G: 700nm, B: 550nm). Unter Verwendung der X,Y,Z-Koordinaten jedes Pixels aus [`georeferenced_pixels.csv`](georeferenced_pixels.csv:1) werden diese drei Bänder zu einem RGB-Bild zusammengesetzt. Dieses Bild wird dann auf ein Raster mit einer Auflösung von 0.1 Metern projiziert und im Koordinatensystem EPSG:32632 (UTM Zone 32N) gespeichert. Die Helligkeitswerte der RGB-Kanäle werden mittels "min_max"-Normalisierung skaliert. Das Endergebnis ist die Datei [`georeferenced_rgb_via_config.tif`](georeferenced_rgb_via_config.tif:1).

### 5. Erstellung der Plots

-   **Funktion im Skript**: [`run_plotting(config_path)`](main_pipeline.py:50) (aufgerufen aus [`plot_hsi_data.py`](plot_hsi_data.py:1))
-   **Beschreibung**: Erstellt verschiedene Diagramme zur visuellen Qualitätskontrolle und Analyse der Ergebnisse aus den vorhergehenden Verarbeitungsschritten.
-   **Eingabe (gemäß [`config.toml`](config.toml:1) und vorherigen Schritten):**
    -   Konsolidierte WebODM Posen: [`webodm_poses_consolidated.csv`](webodm_poses_consolidated.csv:1)
    -   Synchronisierte HSI Posen: [`hsi_poses.csv`](hsi_poses.csv:1)
    -   Georeferenzierte Pixelkoordinaten: [`georeferenced_pixels.csv`](georeferenced_pixels.csv:1) (optional, je nach Plot)
    -   Georeferenziertes RGB GeoTIFF: [`georeferenced_rgb_via_config.tif`](georeferenced_rgb_via_config.tif:1) (optional, je nach Plot)
-   **Ausgabe (gemäß [`config.toml`](config.toml:1)):**
    -   Verschiedene Plot-Dateien (z.B. PNG) im Verzeichnis [`plots/`](plots/:1) (definiert durch `paths.plot_output_directory`). Beispiele könnten sein: [`plots/plot_trajectory_2d.png`](plots/plot_trajectory_2d.png:1), [`plots/plot_orientation_over_index.png`](plots/plot_orientation_over_index.png:1).
    -   Ein boolescher Wert, der den Erfolg des Schritts anzeigt. (Ein Fehlschlag hier führt nur zu einer Warnung).
-   **Beispielhafter Verarbeitungsablauf:**
    Das Skript verwendet die Daten aus den CSV-Dateien (z.B. [`hsi_poses.csv`](hsi_poses.csv:1) für Trajektorien- und Orientierungsplots) und potenziell auch aus dem erzeugten GeoTIFF, um verschiedene Diagramme zu erstellen. Diese Diagramme können die 2D-Flugtrajektorie, die zeitliche Entwicklung der Kameraorientierung (Omega, Phi, Kappa), die Qualität der Interpolation oder Vergleiche von Zeitstempeln visualisieren. Die erzeugten Bilddateien werden im Verzeichnis [`plots/`](plots/:1) abgelegt.

## Konfiguration

Die gesamte Pipeline wird über eine TOML-Datei (standardmäßig [`config.toml`](config.toml:1)) konfiguriert. Diese Datei enthält Pfade zu Eingabe- und Ausgabedateien sowie spezifische Parameter für jeden Verarbeitungsschritt. Die detaillierten Beschreibungen und Beispiele in diesem Dokument basieren auf den spezifischen Einstellungen der [`config.toml`](config.toml:1) Datei des Projekts.

## Ausführung

Das Skript kann direkt ausgeführt werden:
```bash
python main_pipeline.py
```
Optional kann ein Pfad zu einer benutzerdefinierten Konfigurationsdatei übergeben werden (aktuell im Code als Kommentar, könnte via `argparse` implementiert werden).

## Rückgabewert

Die Funktion [`run_complete_pipeline`](main_pipeline.py:9) gibt `True` zurück, wenn alle Schritte (insbesondere die kritischen Schritte 1-3) erfolgreich waren. Sie gibt `False` zurück, wenn einer der kritischen Schritte fehlschlägt oder wenn optionale Schritte (4 oder 5) mit Warnungen abgeschlossen werden.