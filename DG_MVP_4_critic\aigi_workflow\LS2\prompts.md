## Prompt [LS2_1]

### Context
The current handling of lever arm corrections in [`georeference_hsi_pixels.py`](georeference_hsi_pixels.py:1) is ambiguous. It parses `lever_arm_from_hdr` but uses an `effective_lever_arm_body` derived from `config.toml` (defaulting to `(0,0,0)`). This can lead to significant georeferencing inaccuracies if the HSI header contains correct calibrated values. [`synchronize_hsi_webodm.py`](synchronize_hsi_webodm.py:1) also contains a misleading comment about lever arm correction not being implemented. This issue directly impacts the correctness score (currently 20.0).

### Objective
To ensure the HSI Georeferencing Pipeline uses the most accurate lever arm values available, prioritizing calibrated sensor data from the HSI header, while allowing for explicit configuration overrides. This change aims to improve georeferencing accuracy and code clarity.

### Focus Areas
- Prioritizing `lever_arm_from_hdr` in [`georeference_hsi_pixels.py`](georeference_hsi_pixels.py:1).
- Implementing a clear override mechanism via `config.toml`.
- Adding warnings for potentially incorrect (zeroed) lever arm usage.
- Clarifying lever arm comments and documentation.

### Code Reference
- [`georeference_hsi_pixels.py:323-327`](georeference_hsi_pixels.py:323) (lever_arm_from_hdr parsing)
- [`georeference_hsi_pixels.py:400-407`](georeference_hsi_pixels.py:400) (effective_lever_arm_body usage)
- [`georeference_hsi_pixels.py:519`](georeference_hsi_pixels.py:519) (P_sensor_world calculation)
- [`synchronize_hsi_webodm.py:323-329`](synchronize_hsi_webodm.py:323) (comment on lever arm)
- [`main_pipeline_documentation.md:56`](main_pipeline_documentation.md:56) (lever arm documentation)

### Requirements
1.  Modify [`georeference_hsi_pixels.py`](georeference_hsi_pixels.py:1) to use `lever_arm_from_hdr` as the default for georeferencing calculations.
2.  Implement logic in [`georeference_hsi_pixels.py`](georeference_hsi_pixels.py:1) to allow `config.toml` to override `lever_arm_from_hdr` *only if* the lever arm values in `config.toml` are explicitly set and non-zero.
3.  Add a prominent log warning (using the new logging system, see Prompt LS2_4) in [`georeference_hsi_pixels.py`](georeference_hsi_pixels.py:1) if the `effective_lever_arm_body` used in calculations is `(0,0,0)` while `lever_arm_from_hdr` was non-zero and not overridden by a non-zero config value.
4.  Update or remove the misleading comment regarding lever arm correction in [`synchronize_hsi_webodm.py:323-329`](synchronize_hsi_webodm.py:323).
5.  Update [`main_pipeline_documentation.md`](main_pipeline_documentation.md:1) to clearly document the new lever arm source priority (HDR first, then explicit non-zero config override) and its impact.
6.  Write unit tests for the lever arm selection logic, covering scenarios:
    *   Lever arm from HDR only.
    *   Lever arm from config override only (HDR might be zero or different).
    *   Lever arm from HDR, zero config (HDR should be used).
    *   Both HDR and config are zero.
    *   Warning generation when HDR is non-zero and effective arm is zero.

### Expected Improvements
- Increase in georeferencing accuracy (Correctness score: +20 points).
- Improved clarity and maintainability of lever arm logic (Complexity score: +5 points).
- Enhanced test coverage for lever arm functionality (Coverage score: +5 points).

## Prompt [LS2_2]

### Context
The core georeferencing logic in [`georeference_hsi_pixels.py`](georeference_hsi_pixels.py:1) uses nested Python loops for per-pixel processing. This includes computationally intensive operations like matrix multiplications and the iterative `calculate_ray_dsm_intersection` function, making it a significant performance bottleneck (Performance score: 27.0). This approach does not scale well for large HSI datasets.

### Objective
To significantly improve the georeferencing processing speed by vectorizing calculations within [`georeference_hsi_pixels.py`](georeference_hsi_pixels.py:1) using NumPy, thereby reducing execution time and improving scalability.

### Focus Areas
- Vectorizing the transformation of sensor view vectors (`d_sensor_frame`) to world coordinates (`d_world`).
- Vectorizing the flat-plane intersection logic.
- Investigating optimization or batch processing for the `calculate_ray_dsm_intersection` function.

### Code Reference
- [`georeference_hsi_pixels.py:498-590`](georeference_hsi_pixels.py:498) (main processing loops)
- [`georeference_hsi_pixels.py:114-246`](georeference_hsi_pixels.py:114) (`calculate_ray_dsm_intersection` function)
- [`georeference_hsi_pixels.py:47`](georeference_hsi_pixels.py:47) (matrix multiplication `R_sensor_to_world @ d_sensor_frame`)

### Requirements
1.  Refactor the main processing loops in [`georeference_hsi_pixels.py:498-590`](georeference_hsi_pixels.py:498) to utilize NumPy vectorized operations for:
    *   Calculating `d_sensor_frame` for all pixels in a line (or batch of lines).
    *   Transforming `d_sensor_frame` to `d_world` for all pixels in a line (or batch of lines) using matrix multiplication.
2.  Vectorize the flat-plane intersection calculations if this method is chosen (`z_ground_method == "flat_plane"`).
3.  Analyze the `calculate_ray_dsm_intersection` function ([`georeference_hsi_pixels.py:114-246`](georeference_hsi_pixels.py:114)) for opportunities for partial vectorization or batch processing of multiple rays. If full vectorization is too complex for this iteration, identify and implement any feasible micro-optimizations or pre-computation steps that can be done outside the per-pixel call.
4.  Implement performance benchmark tests (e.g., using `timeit` or `pytest-benchmark`) for the georeferencing step with a sample dataset to measure the improvement before and after vectorization.
5.  Ensure memory usage remains manageable after vectorization, especially for large HSI datasets. Consider chunk-based processing if necessary.

### Expected Improvements
- Significant reduction in processing time for georeferencing (Performance score: +30 points).
- Improved scalability for larger HSI datasets (Scalability score: +20 points).
- Introduction of performance benchmark tests (Coverage score: +5 points, indirectly improving overall quality).

## Prompt [LS2_3]

### Context
Currently, each main processing script ([`georeference_hsi_pixels.py`](georeference_hsi_pixels.py:1), [`synchronize_hsi_webodm.py`](synchronize_hsi_webodm.py:1), [`plot_hsi_data.py`](plot_hsi_data.py:1), and `create_consolidated_webodm_poses.py`) independently loads and parses the `config.toml` file. This leads to redundant file I/O, potential inconsistencies if paths are mishandled, and makes centralized configuration management difficult. This affects maintainability and robustness (Complexity score: 30.0, Correctness score: 20.0).

### Objective
To centralize configuration loading by parsing `config.toml` once in [`main_pipeline.py`](main_pipeline.py:1) and passing the loaded configuration object or relevant, validated parameters to the sub-modules.

### Focus Areas
- Modifying [`main_pipeline.py`](main_pipeline.py:1) to be the sole loader of `config.toml`.
- Refactoring sub-modules to accept configuration data as arguments rather than loading it themselves.
- Ensuring consistent parameter usage across the pipeline.

### Code Reference
- [`main_pipeline.py`](main_pipeline.py:1) (specifically `run_complete_pipeline` and calls to `run_*` functions)
- [`georeference_hsi_pixels.py:252-396`](georeference_hsi_pixels.py:252) (config loading section)
- [`synchronize_hsi_webodm.py:281-314`](synchronize_hsi_webodm.py:281) (config loading section)
- [`plot_hsi_data.py:109-125`](plot_hsi_data.py:109) (config loading section)
- `create_consolidated_webodm_poses.py` (assumed similar config loading)

### Requirements
1.  Modify [`main_pipeline.py`](main_pipeline.py:1) to load the `config.toml` file once at the beginning of the `run_complete_pipeline` function.
2.  Update the `run_*` function calls within [`main_pipeline.py`](main_pipeline.py:1) (e.g., `run_consolidation`, `run_synchronization`, `run_georeferencing`, `run_plotting`) to pass the loaded `config` object (or specific, validated parameters extracted from it) to the respective functions in the sub-modules.
3.  Refactor the `run_*` functions in the sub-modules ([`georeference_hsi_pixels.py`](georeference_hsi_pixels.py:1), [`synchronize_hsi_webodm.py`](synchronize_hsi_webodm.py:1), [`plot_hsi_data.py`](plot_hsi_data.py:1), and `create_consolidated_webodm_poses.py`) to accept the configuration data as arguments and remove their individual `toml.load(config_path)` calls.
4.  Ensure that all necessary configuration parameters are correctly accessed from the passed config object/parameters within each sub-module.
5.  Write unit tests to verify that sub-modules correctly receive and utilize configuration data when passed from [`main_pipeline.py`](main_pipeline.py:1). Test with various valid and potentially missing (if applicable and handled gracefully) config parameters.

### Expected Improvements
- Reduced redundancy and improved code maintainability (Complexity score: +10 points, Maintainability Index score: +10 points).
- More robust and consistent configuration handling across the pipeline (Correctness score: +5 points).
- Enhanced test coverage for configuration management (Coverage score: +3 points).

## Prompt [LS2_4]

### Context
The pipeline currently relies heavily on `print()` for status messages, warnings, and errors. Error handling is basic, often catching generic exceptions and returning `False`. There's also a mix of German and English in comments, variable names, and output, reducing clarity. These issues impact debuggability and maintainability (Overall score: 28.0).

### Objective
To implement standardized logging using Python's `logging` module, introduce custom, specific exception classes for better error handling, and enforce English as the standard language for all code elements and developer-facing messages.

### Focus Areas
- Replacing `print()` with the `logging` module throughout all Python scripts.
- Defining and utilizing custom exception classes for pipeline-specific errors.
- Translating all German code elements (comments, identifiers, log messages) to English.

### Code Reference
- All Python files: [`main_pipeline.py`](main_pipeline.py:1), [`georeference_hsi_pixels.py`](georeference_hsi_pixels.py:1), [`synchronize_hsi_webodm.py`](synchronize_hsi_webodm.py:1), [`plot_hsi_data.py`](plot_hsi_data.py:1), `create_consolidated_webodm_poses.py`.
- Example: [`main_pipeline.py:99`](main_pipeline.py:99) (`print(f"Starte komplette HSI Georeferenzierungs-Pipeline...")`)

### Requirements
1.  Integrate Python's `logging` module across all Python scripts.
    *   Configure a root logger in [`main_pipeline.py`](main_pipeline.py:1) (or a dedicated utility module) to set the default log level (e.g., INFO), format, and handlers (e.g., console output).
    *   Replace all `print()` statements currently used for status updates, debugging information, warnings, and errors with appropriate `logger.info()`, `logger.debug()`, `logger.warning()`, `logger.error()` calls.
2.  Define custom, specific exception classes (e.g., `PipelineConfigError`, `HSIDataError`, `SynchronizationError`, `GeoreferencingError`, `DSMIntersectionError`) inheriting from `Exception`.
3.  Modify sub-modules to raise these specific exceptions when errors occur, instead of primarily returning `False` or printing error messages.
4.  Update [`main_pipeline.py`](main_pipeline.py:1) to catch these specific exceptions from sub-module calls, log them appropriately, and decide whether to terminate the pipeline or attempt recovery if feasible.
5.  Translate all German comments, variable names, function names, class names, and string literals intended for logs/developer output to English. Ensure user-facing output from [`plot_hsi_data.py`](plot_hsi_data.py:1) (if any, beyond plot labels) is also considered for internationalization or made configurable if necessary.
6.  Write unit tests to verify:
    *   That appropriate log messages are emitted at different log levels for key operations.
    *   That custom exceptions are raised correctly under specific error conditions.
    *   That [`main_pipeline.py`](main_pipeline.py:1) correctly catches and handles these custom exceptions.

### Expected Improvements
- Improved debuggability and error tracking through structured logging (Correctness score: +10 points).
- Enhanced code clarity, maintainability, and professionalism (Complexity score: +5 points, Maintainability Index score: +10 points).
- More robust error handling capabilities.
- Increased test coverage for logging and error handling mechanisms (Coverage score: +5 points).

## Prompt [LS2_5]

### Context
The current test coverage for the HSI Georeferencing Pipeline is critically low (Coverage score: 7.0, with estimated 0% line/branch coverage). This lack of tests makes it risky to refactor code, verify correctness, and ensure reliability. The `reflection_LS1.md` identifies several areas where clarity is needed (e.g., coordinate systems, sensor model angle interpretation), which also implies a lack of tests for these transformations and interpretations.

### Objective
To significantly increase unit and integration test coverage across the entire HSI Georeferencing Pipeline, focusing on critical calculations, data transformations, module interactions, and validation of configuration and input data. This is foundational for improving all other quality metrics.

### Focus Areas
- Unit testing key functions in all core modules ([`georeference_hsi_pixels.py`](georeference_hsi_pixels.py:1), [`synchronize_hsi_webodm.py`](synchronize_hsi_webodm.py:1), `create_consolidated_webodm_poses.py`).
- Integration testing for major pipeline stages.
- Testing coordinate transformations and sensor model interpretations.
- Validating input data parsing and configuration parameter usage.

### Code Reference
- All Python modules and their core functions.
- Specific areas highlighted in `reflection_LS1.md` Issue 5:
    - [`georeference_hsi_pixels.py:108-110`](georeference_hsi_pixels.py:108) (sensor angle interpretation)
    - [`georeference_hsi_pixels.py:480-482`](georeference_hsi_pixels.py:480) (boresight matrix)
    - [`main_pipeline_documentation.md`](main_pipeline_documentation.md:1) (discrepancies with quaternion usage)

### Requirements
1.  **Unit Tests for Core Logic:**
    *   In [`georeference_hsi_pixels.py`](georeference_hsi_pixels.py:1):
        *   `parse_hsi_header` (mocking file content).
        *   `calculate_sensor_view_vector` for various pixel indices.
        *   `calculate_ray_dsm_intersection` (with a mock/simple DSM and known intersection points).
        *   `transform_enu_to_latlonalt` and its inverse if used.
        *   Logic for sensor model angle interpretation (`vinkelx_deg`, `vinkely_deg`) ensuring correct unit handling and application of scale/offset.
        *   Boresight matrix construction and application.
    *   In [`synchronize_hsi_webodm.py`](synchronize_hsi_webodm.py:1):
        *   `parse_hsi_timestamps_from_hdr` (mocking file content).
        *   `interpolate_poses_to_hsi_timestamps` (with sample pose data and timestamps, verifying interpolation methods like Slerp for quaternions).
    *   In `create_consolidated_webodm_poses.py`: Test core logic for parsing WebODM/MMS data and consolidating poses into the target format.
2.  **Integration Tests for Pipeline Stages:**
    *   Develop integration tests that run data through sequential pairs of pipeline steps (e.g., consolidation -> synchronization, synchronization -> georeferencing) using small, well-defined input datasets and verifying intermediate and final outputs.
3.  **Configuration and Input Validation Tests:**
    *   Test the loading and validation of parameters from `config.toml`.
    *   Test the parsing and validation of input files (e.g., HSI header, pose files, DSM).
4.  **Coordinate System and Transformation Tests:**
    *   Write specific tests to verify the correctness of all key coordinate transformations (e.g., IMU to body, body to sensor, sensor to world, ENU to LatLonAlt) using known input vectors and expected output vectors.
    *   Ensure quaternion usage is consistent and correct.
5.  **Documentation Alignment:** As part of testing transformations and interpretations, ensure that [`main_pipeline_documentation.md`](main_pipeline_documentation.md:1) is updated to reflect the actual tested behavior (e.g., quaternion usage, boresight definition).
6.  Aim to achieve at least 50% line coverage as an initial target for this iteration (LS2). Use a coverage measurement tool (e.g., `coverage.py`) to track progress.

### Expected Improvements
- Significant increase in overall test coverage (Coverage score: +40 points).
- Improved code reliability and robustness due to tested logic (Correctness score: +15 points).
- Safer refactoring and easier identification of regressions in future development.
- Increased confidence in the correctness of transformations and calculations.