"""
Unit tests for lever arm functionality in the HSI Georeferencing Pipeline.

This module tests the lever arm selection logic as specified in LS2_1 requirements.
"""

import pytest
import numpy as np
import logging
from unittest.mock import patch, MagicMock

# Import the modules to test
from src.hsi_pipeline.lever_arm_utils import determine_effective_lever_arm, validate_lever_arm


class TestLeverArmDetermination:
    """Test cases for lever arm determination logic."""
    
    def test_lever_arm_hdr_only(self, caplog):
        """Test Case 1.1: Lever arm from HDR only."""
        # Arrange
        hdr_lever_arm = np.array([0.1, 0.05, -0.02])
        config_lever_arm = np.array([0.0, 0.0, 0.0])  # Zero config
        
        # Act
        with caplog.at_level(logging.INFO):
            effective_arm, config_override_used = determine_effective_lever_arm(
                hdr_lever_arm, config_lever_arm
            )
        
        # Assert
        np.testing.assert_array_almost_equal(effective_arm, hdr_lever_arm)
        assert not config_override_used
        assert "Using lever arm from HSI header" in caplog.text
    
    def test_lever_arm_config_override(self, caplog):
        """Test Case 1.2: Lever arm from config override."""
        # Arrange
        hdr_lever_arm = np.array([0.1, 0.05, -0.02])
        config_lever_arm = np.array([0.15, 0.07, -0.03])  # Non-zero config
        
        # Act
        with caplog.at_level(logging.INFO):
            effective_arm, config_override_used = determine_effective_lever_arm(
                hdr_lever_arm, config_lever_arm
            )
        
        # Assert
        np.testing.assert_array_almost_equal(effective_arm, config_lever_arm)
        assert config_override_used
        assert "Using lever arm from configuration override" in caplog.text
    
    def test_lever_arm_hdr_with_zero_config(self, caplog):
        """Test Case 1.3: Lever arm from HDR, zero config."""
        # Arrange
        hdr_lever_arm = np.array([0.1, 0.05, -0.02])
        config_lever_arm = np.array([0.0, 0.0, 0.0])  # Zero config should not override
        
        # Act
        with caplog.at_level(logging.INFO):
            effective_arm, config_override_used = determine_effective_lever_arm(
                hdr_lever_arm, config_lever_arm
            )
        
        # Assert
        np.testing.assert_array_almost_equal(effective_arm, hdr_lever_arm)
        assert not config_override_used
        assert "Using lever arm from HSI header" in caplog.text
    
    def test_lever_arm_both_zero(self, caplog):
        """Test Case 1.4: Both HDR and config lever arm are zero."""
        # Arrange
        hdr_lever_arm = np.array([0.0, 0.0, 0.0])
        config_lever_arm = np.array([0.0, 0.0, 0.0])
        
        # Act
        with caplog.at_level(logging.WARNING):
            effective_arm, config_override_used = determine_effective_lever_arm(
                hdr_lever_arm, config_lever_arm
            )
        
        # Assert
        np.testing.assert_array_almost_equal(effective_arm, np.array([0.0, 0.0, 0.0]))
        assert not config_override_used
        assert "Both HSI header and configuration provide zero lever arm values" in caplog.text
    
    def test_warning_zero_effective_arm_with_nonzero_hdr(self, caplog):
        """Test Case 1.5: Warning for zero effective arm with non-zero HDR."""
        # This test simulates a scenario where HDR has non-zero values but
        # somehow the effective arm becomes zero (edge case)
        
        # Arrange
        hdr_lever_arm = np.array([0.1, 0.05, -0.02])
        config_lever_arm = None  # No config provided
        
        # Act
        with caplog.at_level(logging.WARNING):
            effective_arm, config_override_used = determine_effective_lever_arm(
                hdr_lever_arm, config_lever_arm
            )
        
        # Assert
        # In this case, HDR should be used since config is None
        np.testing.assert_array_almost_equal(effective_arm, hdr_lever_arm)
        assert not config_override_used
    
    def test_lever_arm_none_inputs(self):
        """Test handling of None inputs."""
        # Test with None HDR
        effective_arm, config_override_used = determine_effective_lever_arm(
            None, np.array([0.1, 0.0, 0.0])
        )
        np.testing.assert_array_almost_equal(effective_arm, np.array([0.1, 0.0, 0.0]))
        assert config_override_used
        
        # Test with None config
        effective_arm, config_override_used = determine_effective_lever_arm(
            np.array([0.1, 0.0, 0.0]), None
        )
        np.testing.assert_array_almost_equal(effective_arm, np.array([0.1, 0.0, 0.0]))
        assert not config_override_used
        
        # Test with both None
        effective_arm, config_override_used = determine_effective_lever_arm(None, None)
        np.testing.assert_array_almost_equal(effective_arm, np.array([0.0, 0.0, 0.0]))
        assert not config_override_used

    def test_dead_code_coverage_line_75(self, caplog):
        """Test to achieve 100% coverage - this exposes potential dead code on line 75."""
        # This test demonstrates that line 75 may be unreachable due to logic flow
        # The condition `if hdr_is_nonzero and not config_is_nonzero:` on line 74
        # can never be True when we're in the else block (lines 68-85)
        # because the else block is only reached when both hdr_is_nonzero and config_is_nonzero are False

        # Let's test the actual reachable path - both zero
        hdr_lever_arm = np.array([0.0, 0.0, 0.0])  # Zero from header
        config_lever_arm = np.array([0.0, 0.0, 0.0])  # Zero from config

        # Act
        with caplog.at_level(logging.WARNING):
            effective_arm, config_override_used = determine_effective_lever_arm(
                hdr_lever_arm, config_lever_arm
            )

        # Assert
        # Should use zero values and trigger line 80-85 warning, not line 75
        np.testing.assert_array_almost_equal(effective_arm, np.array([0.0, 0.0, 0.0]))
        assert not config_override_used
        assert "Both HSI header and configuration provide zero lever arm values" in caplog.text

        # Line 75 appears to be unreachable due to the logic structure
        # This test documents this finding for potential refactoring


class TestLeverArmValidation:
    """Test cases for lever arm validation."""
    
    def test_valid_lever_arm(self):
        """Test validation of valid lever arms."""
        valid_arms = [
            np.array([0.1, 0.05, -0.02]),
            np.array([0.0, 0.0, 0.0]),
            np.array([1.5, -0.8, 0.3]),
        ]
        
        for arm in valid_arms:
            assert validate_lever_arm(arm)
    
    def test_invalid_lever_arm_shape(self, caplog):
        """Test validation with invalid shapes."""
        invalid_arms = [
            np.array([0.1, 0.05]),  # Too few elements
            np.array([0.1, 0.05, -0.02, 0.1]),  # Too many elements
            np.array([[0.1, 0.05, -0.02]]),  # Wrong shape
        ]
        
        with caplog.at_level(logging.ERROR):
            for arm in invalid_arms:
                assert not validate_lever_arm(arm)
        
        assert "must be 3-element array" in caplog.text
    
    def test_invalid_lever_arm_values(self, caplog):
        """Test validation with invalid values."""
        invalid_arms = [
            np.array([np.nan, 0.05, -0.02]),  # NaN
            np.array([0.1, np.inf, -0.02]),  # Infinite
            np.array([0.1, 0.05, -np.inf]),  # Negative infinite
        ]
        
        with caplog.at_level(logging.ERROR):
            for arm in invalid_arms:
                assert not validate_lever_arm(arm)
        
        assert "contains NaN or infinite values" in caplog.text
    
    def test_large_lever_arm_warning(self, caplog):
        """Test warning for unusually large lever arms."""
        large_arm = np.array([15.0, 0.05, -0.02])  # > 10 meters
        
        with caplog.at_level(logging.WARNING):
            result = validate_lever_arm(large_arm)
        
        # Should still be valid but with warning
        assert result
        assert "unusually large magnitude" in caplog.text
    
    def test_none_lever_arm(self):
        """Test validation with None input."""
        assert not validate_lever_arm(None)


class TestIntegrationLeverArm:
    """Integration tests for lever arm functionality."""
    
    @patch('src.hsi_pipeline.lever_arm_utils.logger')
    def test_lever_arm_integration_scenario(self, mock_logger):
        """Test a realistic integration scenario."""
        # Simulate a typical scenario where HDR has calibrated values
        # and config has default zeros
        hdr_lever_arm = np.array([0.12, 0.08, -0.05])  # Realistic calibrated values
        config_lever_arm = np.array([0.0, 0.0, 0.0])   # Default config
        
        effective_arm, config_override_used = determine_effective_lever_arm(
            hdr_lever_arm, config_lever_arm
        )
        
        # Should use HDR values
        np.testing.assert_array_almost_equal(effective_arm, hdr_lever_arm)
        assert not config_override_used
        
        # Verify logging calls
        mock_logger.info.assert_called()
        
    def test_lever_arm_precision(self):
        """Test precision handling in lever arm calculations."""
        # Test with very small but non-zero values
        small_hdr = np.array([1e-8, 1e-8, 1e-8])
        zero_config = np.array([0.0, 0.0, 0.0])
        
        effective_arm, config_override_used = determine_effective_lever_arm(
            small_hdr, zero_config
        )
        
        # Should still use HDR values even if very small
        np.testing.assert_array_almost_equal(effective_arm, small_hdr)
        assert not config_override_used


if __name__ == "__main__":
    # Run tests with pytest
    pytest.main([__file__, "-v"])
