"""
Unit tests for the updated create_consolidated_webodm_poses module.

This module tests the LS2 improvements including centralized configuration,
logging integration, and custom exception handling.
"""

import pytest
import json
import tempfile
import os
from pathlib import Path
from unittest.mock import patch, MagicMock

# Import the modules to test
from src.hsi_pipeline.create_consolidated_webodm_poses import run_consolidation
from src.hsi_pipeline.pipeline_exceptions import PipelineConfigError, WebODMDataError, InputDataError


class TestConsolidationLS2Updates:
    """Test cases for LS2 updates to consolidation module."""
    
    def test_function_signature_accepts_config_dict(self):
        """Test that run_consolidation accepts config dict instead of config_path string."""
        # Arrange
        config = {
            'paths': {
                'webodm_data_directory': 'test_dir',
                'shots_geojson_file': 'shots.geojson',
                'haip_files_subdirectory': 'haip',
                'output_directory': 'output',
                'consolidated_webodm_poses_csv': 'consolidated.csv'
            },
            'parameters': {
                'webodm_consolidation': {
                    'haip_timestamp_key': 'timestamp_rgb_us'
                }
            }
        }
        
        # Act & Assert - Should not raise TypeError for accepting dict
        # We'll mock the file operations to avoid actual file I/O
        with patch('src.hsi_pipeline.create_consolidated_webodm_poses.os.makedirs'), \
             patch('builtins.open', side_effect=FileNotFoundError):
            
            with pytest.raises(InputDataError):  # Expected due to missing files
                run_consolidation(config)
    
    def test_missing_config_key_raises_pipeline_config_error(self):
        """Test that missing configuration keys raise PipelineConfigError."""
        # Arrange
        incomplete_config = {
            'paths': {
                'webodm_data_directory': 'test_dir'
                # Missing other required keys
            }
        }
        
        # Act & Assert
        with pytest.raises(PipelineConfigError, match="Missing configuration key"):
            run_consolidation(incomplete_config)
    
    def test_missing_shots_file_raises_input_data_error(self, tmp_path):
        """Test that missing shots.geojson file raises InputDataError."""
        # Arrange
        config = {
            'paths': {
                'webodm_data_directory': str(tmp_path),
                'shots_geojson_file': 'nonexistent_shots.geojson',
                'haip_files_subdirectory': 'haip',
                'output_directory': str(tmp_path / 'output'),
                'consolidated_webodm_poses_csv': 'consolidated.csv'
            },
            'parameters': {
                'webodm_consolidation': {
                    'haip_timestamp_key': 'timestamp_rgb_us'
                }
            }
        }
        
        # Act & Assert
        with pytest.raises(InputDataError, match="Shots GeoJSON file not found"):
            run_consolidation(config)
    
    def test_invalid_json_raises_webodm_data_error(self, tmp_path):
        """Test that invalid JSON in shots file raises WebODMDataError."""
        # Arrange
        shots_file = tmp_path / 'shots.geojson'
        shots_file.write_text('invalid json content')
        
        config = {
            'paths': {
                'webodm_data_directory': str(tmp_path),
                'shots_geojson_file': 'shots.geojson',
                'haip_files_subdirectory': 'haip',
                'output_directory': str(tmp_path / 'output'),
                'consolidated_webodm_poses_csv': 'consolidated.csv'
            },
            'parameters': {
                'webodm_consolidation': {
                    'haip_timestamp_key': 'timestamp_rgb_us'
                }
            }
        }
        
        # Act & Assert
        with pytest.raises(WebODMDataError, match="Could not decode JSON"):
            run_consolidation(config)
    
    def test_missing_features_key_raises_webodm_data_error(self, tmp_path):
        """Test that missing 'features' key in JSON raises WebODMDataError."""
        # Arrange
        shots_file = tmp_path / 'shots.geojson'
        shots_file.write_text('{"type": "FeatureCollection"}')  # Missing 'features'
        
        config = {
            'paths': {
                'webodm_data_directory': str(tmp_path),
                'shots_geojson_file': 'shots.geojson',
                'haip_files_subdirectory': 'haip',
                'output_directory': str(tmp_path / 'output'),
                'consolidated_webodm_poses_csv': 'consolidated.csv'
            },
            'parameters': {
                'webodm_consolidation': {
                    'haip_timestamp_key': 'timestamp_rgb_us'
                }
            }
        }
        
        # Act & Assert
        with pytest.raises(WebODMDataError, match="'features' key not found"):
            run_consolidation(config)
    
    @patch('src.hsi_pipeline.create_consolidated_webodm_poses.get_logger')
    def test_logging_integration(self, mock_get_logger, tmp_path):
        """Test that logging is properly integrated."""
        # Arrange
        mock_logger = MagicMock()
        mock_get_logger.return_value = mock_logger
        
        shots_file = tmp_path / 'shots.geojson'
        shots_data = {
            "type": "FeatureCollection",
            "features": []
        }
        shots_file.write_text(json.dumps(shots_data))
        
        config = {
            'paths': {
                'webodm_data_directory': str(tmp_path),
                'shots_geojson_file': 'shots.geojson',
                'haip_files_subdirectory': 'haip',
                'output_directory': str(tmp_path / 'output'),
                'consolidated_webodm_poses_csv': 'consolidated.csv'
            },
            'parameters': {
                'webodm_consolidation': {
                    'haip_timestamp_key': 'timestamp_rgb_us'
                }
            }
        }
        
        # Act
        result = run_consolidation(config)
        
        # Assert
        assert result is True
        mock_logger.info.assert_called()
        
        # Check that specific log messages were called
        log_calls = [str(call) for call in mock_logger.info.call_args_list]
        assert any("Starting WebODM pose consolidation" in call for call in log_calls)
        assert any("Found 0 features" in call for call in log_calls)
    
    def test_successful_consolidation_with_valid_data(self, tmp_path):
        """Test successful consolidation with valid data."""
        # Arrange
        haip_dir = tmp_path / 'haip'
        haip_dir.mkdir()
        output_dir = tmp_path / 'output'
        
        # Create a valid HAIP file
        haip_file = haip_dir / 'test_image.haip'
        haip_file.write_text('timestamp_rgb_us: 1234567890.123\n')
        
        # Create valid shots.geojson
        shots_file = tmp_path / 'shots.geojson'
        shots_data = {
            "type": "FeatureCollection",
            "features": [
                {
                    "properties": {
                        "filename": "test_image.jpg",
                        "translation": [100.0, 200.0, 50.0],
                        "rotation": [0.1, 0.2, 0.3]
                    }
                }
            ]
        }
        shots_file.write_text(json.dumps(shots_data))
        
        config = {
            'paths': {
                'webodm_data_directory': str(tmp_path),
                'shots_geojson_file': 'shots.geojson',
                'haip_files_subdirectory': 'haip',
                'output_directory': str(output_dir),
                'consolidated_webodm_poses_csv': 'consolidated.csv'
            },
            'parameters': {
                'webodm_consolidation': {
                    'haip_timestamp_key': 'timestamp_rgb_us'
                }
            }
        }
        
        # Act
        result = run_consolidation(config)
        
        # Assert
        assert result is True
        
        # Check that output file was created
        output_file = output_dir / 'consolidated.csv'
        assert output_file.exists()
        
        # Check content
        content = output_file.read_text()
        assert 'test_image.jpg' in content
        assert '1234567890123' in content  # Timestamp converted to nanoseconds


if __name__ == "__main__":
    # Run tests with pytest
    pytest.main([__file__, "-v"])
