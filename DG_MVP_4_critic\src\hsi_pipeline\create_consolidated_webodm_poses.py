"""
WebODM pose consolidation module for the HSI Georeferencing Pipeline.

This module consolidates WebODM pose data from shots.geojson and HAIP files
into a unified CSV format for further processing.
"""

import json
import csv
import os
from typing import Dict, Any, List

from .logging_config import get_logger
from .pipeline_exceptions import (
    PipelineConfigError, WebODMDataError, InputDataError
)

def run_consolidation(config: Dict[str, Any]) -> bool:
    """
    Consolidate WebODM pose data from shots.geojson and HAIP files.

    Args:
        config: Configuration dictionary loaded from TOML file

    Returns:
        True if consolidation completed successfully, False otherwise

    Raises:
        PipelineConfigError: If configuration is invalid
        WebODMDataError: If WebODM data cannot be processed
        InputDataError: If input files are missing or invalid
    """
    logger = get_logger(__name__)
    logger.info("Starting WebODM pose consolidation")

    # Load paths from config
    try:
        webodm_data_dir = config['paths']['webodm_data_directory']
        shots_geojson_filename = config['paths']['shots_geojson_file']
        shots_geojson_path = os.path.join(webodm_data_dir, shots_geojson_filename)

        haip_subdir = config['paths']['haip_files_subdirectory']
        haip_dir_path = os.path.join(webodm_data_dir, haip_subdir)

        output_dir = config['paths']['output_directory']
        consolidated_csv_filename = config['paths']['consolidated_webodm_poses_csv']
        output_csv_path = os.path.join(output_dir, consolidated_csv_filename)

        # Load parameters from config
        haip_ts_key = config['parameters']['webodm_consolidation']['haip_timestamp_key']

        logger.info(f"WebODM data directory: {webodm_data_dir}")
        logger.info(f"Shots GeoJSON file: {shots_geojson_path}")
        logger.info(f"HAIP files directory: {haip_dir_path}")
        logger.info(f"Output CSV file: {output_csv_path}")

    except KeyError as e:
        raise PipelineConfigError(f"Missing configuration key: {e}") from e

    # Ensure output directory exists
    try:
        os.makedirs(output_dir, exist_ok=True)
        logger.info(f"Output directory created/verified: {output_dir}")
    except OSError as e:
        raise WebODMDataError(f"Error creating output directory {output_dir}: {e}") from e

    consolidated_data = []
    header = [
        'image_filename', 'haip_timestamp_ns',
        'pos_x', 'pos_y', 'pos_z',
        'rot_1', 'rot_2', 'rot_3'
    ]

    # Load shots.geojson file
    logger.info(f"Loading shots GeoJSON file: {shots_geojson_path}")
    try:
        with open(shots_geojson_path, 'r', encoding='utf-8') as f:
            shots_data = json.load(f)
    except FileNotFoundError:
        raise InputDataError(f"Shots GeoJSON file not found: {shots_geojson_path}") from None
    except json.JSONDecodeError as e:
        raise WebODMDataError(f"Could not decode JSON from {shots_geojson_path}: {e}") from e
    except Exception as e:
        raise WebODMDataError(f"Unexpected error reading {shots_geojson_path}: {e}") from e

    if 'features' not in shots_data:
        raise WebODMDataError(f"'features' key not found in {shots_geojson_path}")

    logger.info(f"Found {len(shots_data['features'])} features in shots GeoJSON")

    processed_count = 0
    skipped_count = 0

    for feature in shots_data['features']:
        properties = feature.get('properties', {})
        image_filename = properties.get('filename')
        translation = properties.get('translation')
        rotation = properties.get('rotation')

        # Validate feature data
        if not all([
            image_filename,
            isinstance(translation, list) and len(translation) == 3,
            isinstance(rotation, list) and len(rotation) == 3
        ]):
            logger.warning(
                f"Skipping feature due to missing or invalid data in shots.geojson for: "
                f"{properties.get('filename', 'Unknown feature')}"
            )
            skipped_count += 1
            continue

        base_image_filename, _ = os.path.splitext(image_filename)
        haip_filename = base_image_filename + '.haip'
        haip_filepath = os.path.join(haip_dir_path, haip_filename)
        
        # Process HAIP file to extract timestamp
        timestamp_rgb_ns = None
        try:
            found_rgb_timestamp_key = False
            raw_timestamp_value_str = None

            with open(haip_filepath, 'r', encoding='utf-8') as hf:
                for line in hf:
                    stripped_line = line.strip()
                    if stripped_line.startswith(haip_ts_key + ':'):
                        parts = stripped_line.split(':', 1)
                        if len(parts) == 2:
                            raw_timestamp_value_str = parts[1].strip()
                            try:
                                timestamp_rgb_original = float(raw_timestamp_value_str)
                                timestamp_rgb_ns = int(timestamp_rgb_original * 1000)
                                found_rgb_timestamp_key = True
                                break
                            except ValueError:
                                logger.warning(
                                    f"Could not convert '{haip_ts_key}' timestamp value "
                                    f"'{raw_timestamp_value_str}' to number in {haip_filepath} "
                                    f"for image {image_filename}. Skipping this entry."
                                )
                                timestamp_rgb_ns = None
                                found_rgb_timestamp_key = True
                                break

            if not found_rgb_timestamp_key:
                logger.warning(
                    f"'{haip_ts_key}:' key line not found in {haip_filepath} "
                    f"for image {image_filename}. Skipping this entry."
                )
                skipped_count += 1
                continue

            if found_rgb_timestamp_key and timestamp_rgb_ns is None:
                # This case (ValueError during conversion) was already logged
                skipped_count += 1
                continue

        except FileNotFoundError:
            logger.warning(
                f"HAIP file {haip_filepath} not found for image {image_filename}. "
                f"Skipping this entry."
            )
            skipped_count += 1
            continue
        except Exception as e:
            logger.warning(
                f"Unexpected error processing {haip_filepath} for image {image_filename}: {e}. "
                f"Skipping this entry.", exc_info=True
            )
            skipped_count += 1
            continue
            
        # Successfully processed this feature
        row = {
            'image_filename': image_filename,
            'haip_timestamp_ns': timestamp_rgb_ns,
            'pos_x': translation[0],
            'pos_y': translation[1],
            'pos_z': translation[2],
            'rot_1': rotation[0],
            'rot_2': rotation[1],
            'rot_3': rotation[2]
        }
        consolidated_data.append(row)
        processed_count += 1

    # Log processing summary
    logger.info(f"Processing complete: {processed_count} entries processed, {skipped_count} entries skipped")

    if not consolidated_data:
        logger.warning("No data was successfully processed. Output CSV will not be created.")
        # This is considered a successful execution even if no data was processed,
        # as long as no errors occurred during file reading and configuration loading.
        # The lack of processable data might be due to data quality issues rather than
        # pipeline errors.

    # Write consolidated data to CSV
    try:
        with open(output_csv_path, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.DictWriter(csvfile, fieldnames=header)
            writer.writeheader()
            writer.writerows(consolidated_data)

        logger.info(f"Successfully created {output_csv_path} with {len(consolidated_data)} entries")
        return True

    except IOError as e:
        raise WebODMDataError(f"Could not write to {output_csv_path}: {e}") from e
    except Exception as e:
        raise WebODMDataError(f"Unexpected error writing to {output_csv_path}: {e}") from e


if __name__ == "__main__":
    """
    Command-line interface for WebODM pose consolidation.

    This allows the module to be run independently for testing purposes.
    In production, this function should be called from the main pipeline.
    """
    import toml
    from .logging_config import setup_logging

    DEFAULT_CONFIG_PATH = 'config.toml'

    # Setup logging for standalone execution
    setup_logging(log_level="INFO", log_file="consolidation.log")
    logger = get_logger(__name__)

    logger.info(f"Running WebODM pose consolidation with configuration: {DEFAULT_CONFIG_PATH}")

    try:
        # Load configuration
        config = toml.load(DEFAULT_CONFIG_PATH)

        # Run consolidation
        success = run_consolidation(config)

        if success:
            logger.info("WebODM pose consolidation completed successfully")
        else:
            logger.error("WebODM pose consolidation failed")

    except Exception as e:
        logger.error(f"Error during WebODM pose consolidation: {e}", exc_info=True)