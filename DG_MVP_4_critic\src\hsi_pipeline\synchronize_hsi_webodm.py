"""
HSI-WebODM synchronization module for the HSI Georeferencing Pipeline.

This module synchronizes HSI line scan timestamps with WebODM pose data
through temporal interpolation to provide pose information for each HSI line.
"""

import json
import numpy as np
import pandas as pd
from scipy.spatial.transform import Rotation, Slerp
from datetime import datetime, timezone
import os
from typing import Dict, Any, List, Tuple, Optional

from .logging_config import get_logger
from .pipeline_exceptions import (
    PipelineConfigError, HSIDataError, SynchronizationError, InputDataError
)

# --- Helper Functions ---

def parse_hdr_file(hdr_file_path: str) -> Optional[Dict[str, Any]]:
    """
    Parse the ENVI header file to extract metadata.

    Specifically looks for 'lines' and 'OffsetBetweenMainAntennaAndTargetPoint'.

    Args:
        hdr_file_path: Path to the ENVI header file

    Returns:
        Dictionary containing metadata, or None if parsing fails

    Raises:
        HSIDataError: If header file cannot be parsed
    """
    logger = get_logger(__name__)
    metadata = {}

    try:
        with open(hdr_file_path, "r") as f:
            for line in f:
                line = line.strip()
                if "=" in line:
                    key_value = line.split("=", 1)
                    key = key_value[0].strip()
                    value = key_value[1].strip().replace("{", "").replace("}", "")
                    if key == "lines":
                        metadata["lines"] = int(value)
                    elif key == "OffsetBetweenMainAntennaAndTargetPoint (x,y,z)":
                        try:
                            coords_str = (
                                value.replace("(", "").replace(")", "").split(",")
                            )
                            metadata["lever_arm_xyz_mm"] = [
                                int(c.strip()) for c in coords_str
                            ]
                        except ValueError:
                            logger.warning(f"Could not parse lever arm: {value}")
                            metadata["lever_arm_xyz_mm"] = None

    except FileNotFoundError:
        raise InputDataError(f"Header file not found: {hdr_file_path}") from None
    except Exception as e:
        raise HSIDataError(f"Error parsing header file {hdr_file_path}: {e}") from e

    if "lines" not in metadata:
        logger.warning(f"'lines' not found in {hdr_file_path}")

    return metadata


def convert_hsi_timestamp_to_ns(timestamp_str: str) -> int:
    """
    Convert HSI timestamps (assumed to be in microseconds) to nanoseconds.

    Args:
        timestamp_str: Timestamp string in microseconds

    Returns:
        Timestamp in nanoseconds
    """
    return int(timestamp_str) * 1000  # HSI sync file timestamps are in microseconds


def load_hsi_data(sync_file_path: str, total_lines_from_hdr: int) -> List[Tuple[int, int]]:
    """
    Load HSI synchronization data.

    Timestamps are expected to be in nanoseconds.
    Frame/Line numbers are 1-based and in reverse order in the file.
    Corrects line numbers to be 0-based and in acquisition order.

    Args:
        sync_file_path: Path to HSI synchronization file
        total_lines_from_hdr: Total number of lines from HDR file

    Returns:
        List of tuples: (corrected_hsi_line_index, hsi_timestamp_ns) sorted by timestamp

    Raises:
        HSIDataError: If HSI sync file cannot be processed
    """
    logger = get_logger(__name__)
    hsi_data = []

    try:
        with open(sync_file_path, "r") as f:
            lines = f.readlines()
            if not lines or "Frame/Line" not in lines[0]:
                raise HSIDataError(f"Invalid HSI sync file format: {sync_file_path}")

            # Skip header
            for line_content in lines[1:]:
                parts = line_content.strip().split()
                if len(parts) == 2:
                    try:
                        original_line_num = int(parts[0])
                        timestamp_ns = convert_hsi_timestamp_to_ns(parts[1])
                        hsi_data.append(
                            {
                                "original_line_num": original_line_num,
                                "timestamp_ns": timestamp_ns,
                            }
                        )
                    except ValueError:
                        logger.warning(
                            f"Skipping invalid line in HSI sync file: {line_content.strip()}"
                        )
                        continue
                else:
                    logger.warning(
                        f"Skipping malformed line in HSI sync file: {line_content.strip()}"
                    )

        if not hsi_data:
            raise HSIDataError("No data parsed from HSI sync file")

        hsi_data.sort(key=lambda x: x["timestamp_ns"])

        processed_hsi_data = []
        for i, record in enumerate(hsi_data):
            corrected_hsi_line_index = i
            if corrected_hsi_line_index >= total_lines_from_hdr:
                logger.warning(
                    f"Corrected HSI line index {corrected_hsi_line_index} exceeds total lines "
                    f"from HDR {total_lines_from_hdr-1}. Original line num: {record['original_line_num']}"
                )
            processed_hsi_data.append(
                (corrected_hsi_line_index, record["timestamp_ns"])
            )

        if len(processed_hsi_data) != total_lines_from_hdr:
            logger.warning(
                f"Number of lines in sync file ({len(processed_hsi_data)}) does not match "
                f"'lines' in HDR ({total_lines_from_hdr})"
            )
        return processed_hsi_data

    except FileNotFoundError:
        raise InputDataError(f"HSI sync file not found: {sync_file_path}") from None
    except Exception as e:
        raise HSIDataError(f"Error loading HSI data from {sync_file_path}: {e}") from e


def load_webodm_data(webodm_csv_path: str) -> List[Dict[str, Any]]:
    """
    Load WebODM pose data from the consolidated CSV file.

    Extracts timestamp, translation, and rotation.
    Timestamp ('haip_timestamp_ns') is already in nanoseconds.

    Args:
        webodm_csv_path: Path to consolidated WebODM CSV file

    Returns:
        List of dictionaries: {'timestamp_ns': ..., 'translation': ..., 'rotation_vec': ...}
        sorted by timestamp

    Raises:
        InputDataError: If WebODM CSV file is missing or invalid
        SynchronizationError: If data processing fails
    """
    logger = get_logger(__name__)
    webodm_poses = []

    try:
        df = pd.read_csv(webodm_csv_path)
        required_cols = [
            "haip_timestamp_ns",
            "pos_x", "pos_y", "pos_z",
            "rot_1", "rot_2", "rot_3",
        ]
        missing_cols = [col for col in required_cols if col not in df.columns]
        if missing_cols:
            raise SynchronizationError(
                f"Missing required columns in {webodm_csv_path}: {', '.join(missing_cols)}"
            )

        df = df.sort_values(by="haip_timestamp_ns")
        logger.info(f"Loaded {len(df)} WebODM pose records from CSV")

        for index, row in df.iterrows():
            try:
                timestamp_ns = int(row["haip_timestamp_ns"])
                translation = np.array([row["pos_x"], row["pos_y"], row["pos_z"]])
                rotation_vec = np.array([row["rot_1"], row["rot_2"], row["rot_3"]])
                webodm_poses.append(
                    {
                        "timestamp_ns": timestamp_ns,
                        "translation": translation,
                        "rotation_vec": rotation_vec,
                        "original_filename": row.get("image_filename", "N/A"),
                    }
                )
            except ValueError as ve:
                logger.warning(
                    f"Skipping row {index} in {webodm_csv_path} due to data conversion error: {ve}"
                )
                continue
            except Exception as e:
                logger.warning(
                    f"Skipping row {index} in {webodm_csv_path} due to unexpected error: {e}",
                    exc_info=True
                )
                continue

        logger.info(f"Successfully processed {len(webodm_poses)} WebODM poses")
        return webodm_poses

    except FileNotFoundError:
        raise InputDataError(f"WebODM CSV file not found: {webodm_csv_path}") from None
    except pd.errors.EmptyDataError:
        raise InputDataError(f"WebODM CSV file is empty: {webodm_csv_path}") from None
    except Exception as e:
        raise SynchronizationError(f"Error loading WebODM data from {webodm_csv_path}: {e}") from e


def interpolate_pose(hsi_timestamp_ns: int, webodm_poses: List[Dict[str, Any]]) -> Tuple[Optional[np.ndarray], Optional[Rotation], float, float]:
    """
    Interpolate camera pose (translation and rotation) for a given HSI timestamp.

    Linear interpolation for translation, SLERP for rotation.
    Calculates the absolute time differences to the previous (ts_before) and next (ts_after) WebODM timestamps.
    Handles edge cases where HSI timestamp is outside WebODM timestamp range.

    Args:
        hsi_timestamp_ns: HSI timestamp in nanoseconds
        webodm_poses: List of WebODM pose dictionaries

    Returns:
        Tuple of (translation, rotation_object, delta_t_prev_ns, delta_t_next_ns)
    """
    logger = get_logger(__name__)

    if not webodm_poses:
        return None, None, np.nan, np.nan

    p_before = None
    p_after = None

    for pose in webodm_poses:
        if pose["timestamp_ns"] <= hsi_timestamp_ns:
            p_before = pose
        else:
            p_after = pose
            break

    if p_before is None:
        # HSI timestamp is before all WebODM timestamps
        ts_first_webodm = webodm_poses[0]["timestamp_ns"]
        delta_t_prev = np.nan
        delta_t_next = float(abs(hsi_timestamp_ns - ts_first_webodm))
        return (
            webodm_poses[0]["translation"],
            Rotation.from_rotvec(webodm_poses[0]["rotation_vec"]),
            delta_t_prev,
            delta_t_next,
        )

    if p_after is None:
        # HSI timestamp is after all WebODM timestamps
        ts_last_webodm = webodm_poses[-1]["timestamp_ns"]
        delta_t_prev = float(abs(hsi_timestamp_ns - ts_last_webodm))
        delta_t_next = np.nan
        return (
            webodm_poses[-1]["translation"],
            Rotation.from_rotvec(webodm_poses[-1]["rotation_vec"]),
            delta_t_prev,
            delta_t_next,
        )

    ts_b = p_before["timestamp_ns"]
    ts_a = p_after["timestamp_ns"]
    delta_t_prev = float(abs(hsi_timestamp_ns - ts_b))
    delta_t_next = float(abs(hsi_timestamp_ns - ts_a))

    if ts_b == ts_a:
        return (
            p_before["translation"],
            Rotation.from_rotvec(p_before["rotation_vec"]),
            delta_t_prev,
            delta_t_next,
        )

    if ts_b == hsi_timestamp_ns:
        return (
            p_before["translation"],
            Rotation.from_rotvec(p_before["rotation_vec"]),
            delta_t_prev,
            delta_t_next,
        )

    # Perform interpolation
    t_diff_total = ts_a - ts_b
    alpha = (hsi_timestamp_ns - ts_b) / t_diff_total

    interp_translation = (1 - alpha) * p_before["translation"] + alpha * p_after["translation"]

    try:
        rot_before = Rotation.from_rotvec(p_before["rotation_vec"])
        rot_after = Rotation.from_rotvec(p_after["rotation_vec"])
    except ValueError as e:
        logger.error(f"Error converting rotation vector for SLERP: {e}")
        logger.error(f"p_before rot_vec: {p_before['rotation_vec']}, p_after rot_vec: {p_after['rotation_vec']}")
        fallback_rot = rot_before if alpha < 0.5 else rot_after
        return interp_translation, fallback_rot, delta_t_prev, delta_t_next

    key_rots = Rotation.concatenate([rot_before, rot_after])
    key_times = [0, 1]
    slerp_interpolator = Slerp(key_times, key_rots)
    interp_rotation = slerp_interpolator([alpha])[0]
    return interp_translation, interp_rotation, delta_t_prev, delta_t_next


def run_synchronization(config: Dict[str, Any]) -> bool:
    """
    Main processing logic for HSI and WebODM data synchronization.

    Loads HSI and WebODM data, interpolates poses, and saves synchronized results.

    Args:
        config: Configuration dictionary loaded from TOML file

    Returns:
        True if synchronization completed successfully, False otherwise

    Raises:
        PipelineConfigError: If configuration is invalid
        SynchronizationError: If synchronization process fails
        HSIDataError: If HSI data cannot be processed
        InputDataError: If input files are missing or invalid
    """
    logger = get_logger(__name__)
    logger.info("Starting HSI-WebODM synchronization")

    # --- Path Definitions from Configuration ---
    try:
        hsi_data_dir = config['paths']['hsi_data_directory']
        hsi_base_filename = config['paths']['hsi_base_filename']
        hsi_sync_filename_stem = hsi_base_filename.replace("_cont", "")
        hsi_sync_file = os.path.join(hsi_data_dir, f"{hsi_sync_filename_stem}_sync.txt")
        hsi_hdr_file = os.path.join(hsi_data_dir, f"{hsi_base_filename}.hdr")

        output_dir_from_config = config['paths']['output_directory']
        consolidated_poses_filename = config['paths']['consolidated_webodm_poses_csv']
        webodm_csv_file = os.path.join(output_dir_from_config, consolidated_poses_filename)

        hsi_poses_output_filename = config['paths']['hsi_poses_csv']
        output_csv_file = os.path.join(output_dir_from_config, hsi_poses_output_filename)

        logger.info(f"HSI data directory: {hsi_data_dir}")
        logger.info(f"HSI sync file: {hsi_sync_file}")
        logger.info(f"HSI header file: {hsi_hdr_file}")
        logger.info(f"WebODM CSV file: {webodm_csv_file}")
        logger.info(f"Output CSV file: {output_csv_file}")

    except KeyError as e:
        raise PipelineConfigError(f"Missing configuration key: {e}") from e

    # Ensure output directory from config exists
    try:
        os.makedirs(output_dir_from_config, exist_ok=True)
        logger.info(f"Output directory created/verified: {output_dir_from_config}")
    except OSError as e:
        raise SynchronizationError(f"Error creating output directory {output_dir_from_config}: {e}") from e

    # 1. Load HSI Metadata
    logger.info(f"Loading HSI header file: {hsi_hdr_file}")
    try:
        hsi_metadata = parse_hdr_file(hsi_hdr_file)
        if hsi_metadata is None or "lines" not in hsi_metadata:
            raise HSIDataError("Could not load essential HSI metadata (number of lines)")

        total_hsi_lines = hsi_metadata["lines"]
        lever_arm_mm = hsi_metadata.get("lever_arm_xyz_mm")
        logger.info(f"Total HSI lines from HDR: {total_hsi_lines}")

        if lever_arm_mm:
            logger.info(f"Lever arm (x,y,z) in mm: {lever_arm_mm}")
            logger.info("Note: Lever arm correction is handled in the georeferencing module")
        else:
            logger.info("Lever arm information not found or not parsed from HDR")

    except (HSIDataError, InputDataError) as e:
        logger.error(f"Error loading HSI metadata: {e}")
        raise

    # 2. Load HSI Synchronization Data
    logger.info(f"Loading HSI sync data from: {hsi_sync_file}")
    try:
        hsi_sync_data = load_hsi_data(hsi_sync_file, total_hsi_lines)
        if not hsi_sync_data:
            raise HSIDataError("No HSI sync data loaded")
        logger.info(f"Loaded {len(hsi_sync_data)} HSI linescans with timestamps")

    except (HSIDataError, InputDataError) as e:
        logger.error(f"Error loading HSI sync data: {e}")
        raise

    # 3. Load WebODM Pose Data
    logger.info(f"Loading WebODM pose data from: {webodm_csv_file}")
    try:
        webodm_poses = load_webodm_data(webodm_csv_file)
        if not webodm_poses:
            raise SynchronizationError("No WebODM pose data loaded")
        logger.info(f"Loaded {len(webodm_poses)} WebODM poses")

    except (InputDataError, SynchronizationError) as e:
        logger.error(f"Error loading WebODM pose data: {e}")
        raise

    # 4. Synchronize and Interpolate
    logger.info("Synchronizing HSI linescans with WebODM poses...")
    output_data = []
    interpolation_failures = 0

    for hsi_line_index, hsi_ts_ns in hsi_sync_data:
        (
            interp_translation,
            interp_rotation_obj,
            time_diff_prev_ns,
            time_diff_next_ns,
        ) = interpolate_pose(hsi_ts_ns, webodm_poses)

        if interp_translation is not None and interp_rotation_obj is not None:
            quat = interp_rotation_obj.as_quat()  # [x, y, z, w]
            time_diff_prev_ms = time_diff_prev_ns / 1_000_000.0 if not np.isnan(time_diff_prev_ns) else np.nan
            time_diff_next_ms = time_diff_next_ns / 1_000_000.0 if not np.isnan(time_diff_next_ns) else np.nan
            output_data.append(
                {
                    "hsi_line_index": hsi_line_index,
                    "hsi_timestamp_ns": hsi_ts_ns,
                    "pos_x": interp_translation[0],
                    "pos_y": interp_translation[1],
                    "pos_z": interp_translation[2],
                    "quat_x": quat[0],
                    "quat_y": quat[1],
                    "quat_z": quat[2],
                    "quat_w": quat[3],
                    "time_diff_to_prev_ms": time_diff_prev_ms,
                    "time_diff_to_next_ms": time_diff_next_ms,
                }
            )
        else:
            logger.warning(
                f"Could not interpolate pose for HSI line {hsi_line_index} (timestamp {hsi_ts_ns})"
            )
            interpolation_failures += 1

    logger.info(f"Synchronization complete: {len(output_data)} successful, {interpolation_failures} failed")

    # 5. Save Output
    if not output_data:
        logger.warning("No data to save. Output file will not be created.")
        # This is considered a successful execution even if no data was synchronized,
        # as long as no errors occurred during file reading and processing.
        # The lack of synchronizable data might be due to temporal misalignment
        # rather than pipeline errors.
        return True

    df_output = pd.DataFrame(output_data)
    try:
        df_output.to_csv(output_csv_file, index=False)
        logger.info(f"Successfully synchronized and saved HSI poses to {output_csv_file}")
    except Exception as e:
        raise SynchronizationError(f"Error saving output CSV to {output_csv_file}: {e}") from e

    logger.info("HSI-WebODM synchronization completed successfully")
    return True


if __name__ == "__main__":
    """
    Command-line interface for HSI-WebODM synchronization.

    This allows the module to be run independently for testing purposes.
    In production, this function should be called from the main pipeline.
    """
    import toml
    from .logging_config import setup_logging

    DEFAULT_CONFIG_PATH = 'config.toml'

    # Setup logging for standalone execution
    setup_logging(log_level="INFO", log_file="synchronization.log")
    logger = get_logger(__name__)

    logger.info(f"Running HSI pose synchronization with configuration: {DEFAULT_CONFIG_PATH}")

    try:
        # Load configuration
        config = toml.load(DEFAULT_CONFIG_PATH)

        # Run synchronization
        success = run_synchronization(config)

        if success:
            logger.info("HSI pose synchronization completed successfully")
        else:
            logger.error("HSI pose synchronization failed")

    except Exception as e:
        logger.error(f"Error during HSI pose synchronization: {e}", exc_info=True)
