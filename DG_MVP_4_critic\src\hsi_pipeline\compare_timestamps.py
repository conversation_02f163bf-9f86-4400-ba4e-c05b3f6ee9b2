import json
import os
from datetime import datetime, timedel<PERSON>

def parse_haip_timestamp(filepath):
    """Parses a HAIP file to extract the rgb timestamp."""
    try:
        with open(filepath, 'r') as f:
            for line in f:
                if line.strip().startswith('rgb:'):
                    return int(line.split(':')[1].strip())
    except Exception as e:
        print(f"Error reading or parsing HAIP file {filepath}: {e}")
    return None

def main():
    shots_geojson_path = 'data/WebODM/shots.geojson'
    haip_files_dir = 'data/WebODM/haip_files/'

    webodm_timestamps = {}
    try:
        with open(shots_geojson_path, 'r') as f:
            shots_data = json.load(f)
            for feature in shots_data.get('features', []):
                properties = feature.get('properties', {})
                filename = properties.get('filename')
                capture_time_sec = properties.get('capture_time')
                if filename and capture_time_sec is not None:
                    # Convert seconds to microseconds
                    webodm_timestamps[os.path.splitext(filename)[0]] = int(capture_time_sec * 1_000_000)
    except FileNotFoundError:
        print(f"Error: {shots_geojson_path} not found.")
        return
    except json.JSONDecodeError:
        print(f"Error: Could not decode JSON from {shots_geojson_path}.")
        return
    except Exception as e:
        print(f"An unexpected error occurred while processing {shots_geojson_path}: {e}")
        return

    if not webodm_timestamps:
        print("No data loaded from shots.geojson. Exiting.")
        return

    print(f"{'Filename':<40} | {'WebODM Timestamp (µs)':<25} | {'HAIP RGB Timestamp (µs)':<25} | {'Difference (ms)':<15}")
    print("-" * 110)

    haip_file_names = sorted([f for f in os.listdir(haip_files_dir) if f.endswith('.haip')])

    if not haip_file_names:
        print(f"No .haip files found in {haip_files_dir}")
        return

    for haip_filename_full in haip_file_names:
        haip_filepath = os.path.join(haip_files_dir, haip_filename_full)
        haip_rgb_timestamp_us = parse_haip_timestamp(haip_filepath)

        if haip_rgb_timestamp_us is None:
            print(f"{os.path.splitext(haip_filename_full)[0]:<40} | {'N/A':<25} | {'Error reading HAIP':<25} | {'N/A':<15}")
            continue

        haip_base_filename = os.path.splitext(haip_filename_full)[0]

        if haip_base_filename in webodm_timestamps:
            webodm_time_us = webodm_timestamps[haip_base_filename]
            difference_us = haip_rgb_timestamp_us - webodm_time_us
            difference_ms = difference_us / 1000.0
            
            # Convert timestamps to human-readable format for verification (optional)
            # webodm_dt = datetime.fromtimestamp(webodm_time_us / 1_000_000)
            # haip_dt = datetime.fromtimestamp(haip_rgb_timestamp_us / 1_000_000)

            print(f"{haip_base_filename:<40} | {webodm_time_us:<25} | {haip_rgb_timestamp_us:<25} | {difference_ms:<15.3f}")
        else:
            print(f"{haip_base_filename:<40} | {'Not found in WebODM':<25} | {haip_rgb_timestamp_us:<25} | {'N/A':<15}")

if __name__ == '__main__':
    main()