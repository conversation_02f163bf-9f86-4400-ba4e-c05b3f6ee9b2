# Test Specifications for Layer LS6

## Introduction

This document outlines the test specifications for Layer LS6, addressing issues and improvements identified in [`prompts_LS6.md`](prompts_LS6.md), based on feedback from [`reflection_LS5.md`](reflection_LS5.md) and [`scores_LS5.json`](scores_LS5.json). The primary goals are to fix critical bugs in vectorized processing, significantly improve test coverage for `vectorized_georef.py`, and enhance the robustness of DSM file path resolution.

## General Test Setup Considerations

- Mocking will be extensively used via `pytest-mock` (e.g., `mocker.patch`) for external dependencies and functions not directly under test.
- Test data should be representative of real-world scenarios, including valid inputs, edge cases, and error conditions.
- The `pytest` framework is assumed for test structure, fixtures, and execution.
- Fixtures should be utilized for common setup (e.g., sample data, configurations) and teardown.
- Numerical comparisons will use `numpy.testing.assert_allclose` for floating-point values.

## LS6_1: Fix Critical Bug in Vectorized Path Invocation

### Objective
Ensure `run_georeferencing` in [`georeference_hsi_pixels.py`](georeference_hsi_pixels.py:1) correctly invokes `vectorized_georef.process_hsi_line_vectorized` for flat-plane calculations and processes its results accurately. This addresses Issue 1 from [`reflection_LS5.md:29-37`](reflection_LS5.md:29-37).

### Test Cases for `run_georeferencing` (to be added/modified in [`test_georeferencing.py`](test_georeferencing.py:1))

#### Test Case 1.1: Correct Invocation of `process_hsi_line_vectorized` for Flat-Plane
- **Description**: Verify that `run_georeferencing` calls `vectorized_georef.process_hsi_line_vectorized` with the correct `pose_data` structure (raw pose components like `pos_x`, `quat_w`), `z_ground_flat_plane`, and `z_ground_method="flat_plane"` when the flat-plane method is selected and vectorized processing is enabled.
- **Inputs**:
    - `hsi_data_list`: A list of dictionaries, each representing HSI line data (e.g., containing a 'timestamp' and 'data' array).
    - `pose_data_df`: A Pandas DataFrame containing time-indexed pose information (columns like `pos_x`, `pos_y`, `pos_z`, `quat_w`, `quat_x`, `quat_y`, `quat_z`, `timestamp`).
    - `sensor_model`: A dictionary containing sensor model parameters (e.g., `IFOV_x_rad`, `IFOV_y_rad`, `num_pixels_per_line`).
    - `lever_arm_params`: A dictionary containing lever arm components.
    - `config`: A dictionary simulating the pipeline configuration, specifying `georeferencing_params.z_ground_calculation_method = "flat_plane"`, a `georeferencing_params.Z_ground_flat_plane` value, and `georeferencing_params.use_vectorized_processing = True`.
- **Actions**:
    1. Call `run_georeferencing` with the specified inputs.
    2. Mock `georeference_hsi_pixels.vectorized_georef.process_hsi_line_vectorized` to intercept its arguments.
- **Expected Outputs/Behavior**:
    - `vectorized_georef.process_hsi_line_vectorized` is called for each HSI line.
    - The `pose_data` argument passed to the mock for each call must contain raw pose components (e.g., `pos_x`, `quat_x`) corresponding to the `current_pose` for that HSI line. It should NOT contain pre-calculated `P_imu_world` or `R_body_to_world`.
    - The `z_ground_flat_plane` argument passed to the mock matches the value from the input `config`.
    - The `z_ground_method` argument passed to the mock is the string `"flat_plane"`.
- **Acceptance Criteria**:
    - All mock assertions regarding the arguments passed to `process_hsi_line_vectorized` (specifically `pose_data` structure and content, `z_ground_flat_plane`, and `z_ground_method`) pass.
- **Test Scaffolding (pytest)**:
    ```python
    from unittest.mock import patch, ANY
    import pandas as pd
    import numpy as np
    # from georeference_hsi_pixels import run_georeferencing # Target function

    def test_run_georeferencing_flat_plane_vectorized_invocation(mocker):
        # Arrange
        mock_process_vectorized = mocker.patch('georeference_hsi_pixels.vectorized_georef.process_hsi_line_vectorized')
        
        sample_pose_dict = {'pos_x': 10.0, 'pos_y': 20.0, 'pos_z': 100.0, 
                            'quat_w': 1.0, 'quat_x': 0.0, 'quat_y': 0.0, 'quat_z': 0.0,
                            'timestamp': 12345.000}
        pose_data_df = pd.DataFrame([sample_pose_dict]).set_index('timestamp')
        
        hsi_data_list = [{'timestamp': 12345.000, 'data': np.random.rand(5, 3)}] # 1 line, 5 pixels
        
        sensor_model = {'IFOV_x_rad': 0.001, 'IFOV_y_rad': 0.001, 'num_pixels_per_line': 5,
                        'R_sensor_to_body': np.eye(3), 'boresight_vector_sensor': np.array([0,0,1])}
        lever_arm_params = {'main_x': 0.1, 'main_y': 0.0, 'main_z': -0.2, 'is_primary': True}
        
        Z_ground_flat_plane_val = 10.0
        config = {
            'georeferencing_params': {
                'z_ground_calculation_method': 'flat_plane',
                'Z_ground_flat_plane': Z_ground_flat_plane_val,
                'use_vectorized_processing': True,
                'd_world_z_threshold': 0.1 # Example value
            },
            'sensor_params': sensor_model, # Assuming sensor_model is part of config or passed directly
            'lever_arm': {'main_antenna_lever_arm_body': lever_arm_params}
        }

        # Mock return value for process_hsi_line_vectorized to allow run_georeferencing to complete
        mock_process_vectorized.return_value = [
            {'hsi_line_index': 0, 'pixel_index': j, 'X_ground': 1.0, 'Y_ground': 2.0, 'Z_ground': Z_ground_flat_plane_val}
            for j in range(sensor_model['num_pixels_per_line'])
        ]

        # Act
        # run_georeferencing(hsi_data_list, pose_data_df, sensor_model, lever_arm_params, config) # Actual call

        # Assert
        # mock_process_vectorized.assert_called_once() # Or more specific if multiple lines
        # call_args = mock_process_vectorized.call_args
        # assert call_args is not None, "process_hsi_line_vectorized was not called"
        
        # passed_kwargs = call_args.kwargs
        # passed_pose_data = passed_kwargs['pose_data']
        # assert 'pos_x' in passed_pose_data and passed_pose_data['pos_x'] == sample_pose_dict['pos_x']
        # assert 'quat_w' in passed_pose_data and passed_pose_data['quat_w'] == sample_pose_dict['quat_w']
        # assert 'P_imu_world' not in passed_pose_data # Crucial check
        # assert 'R_body_to_world' not in passed_pose_data # Crucial check
        # assert passed_kwargs['z_ground_flat_plane'] == Z_ground_flat_plane_val
        # assert passed_kwargs['z_ground_method'] == "flat_plane"
        pass # Placeholder for actual call and detailed assertions
    ```

#### Test Case 1.2: Correct Result Processing from `process_hsi_line_vectorized`
- **Description**: Verify that `run_georeferencing` correctly processes the `List[Dict]` returned by `vectorized_georef.process_hsi_line_vectorized` into its main `results` list (or DataFrame) and accurately updates `nan_intersection_count`.
- **Inputs**:
    - Same inputs as Test Case 1.1.
    - Mock `vectorized_georef.process_hsi_line_vectorized` to return a known `List[Dict]` structure, including some entries with `np.nan` for coordinates to test `nan_intersection_count`.
- **Actions**:
    1. Call `run_georeferencing` with the specified inputs.
- **Expected Outputs/Behavior**:
    - The main `results` list (or DataFrame, depending on `run_georeferencing`'s return type) is populated correctly.
    - Each entry in `results` corresponds to a dictionary from the mocked return list, containing keys like `hsi_line_index`, `pixel_index`, `X_ground`, `Y_ground`, `Z_ground`.
    - `nan_intersection_count` is correctly incremented for each pixel where `X_ground` (or an equivalent validity flag) indicates an invalid intersection (e.g., is `np.nan`).
- **Acceptance Criteria**:
    - The structure and content of the final `results` data match the expected transformation of the mocked `List[Dict]`.
    - The final `nan_intersection_count` is correct based on the mocked data.
- **Test Scaffolding (pytest)**:
    ```python
    # from georeference_hsi_pixels import run_georeferencing

    def test_run_georeferencing_flat_plane_vectorized_result_processing(mocker):
        # Arrange
        mock_process_vectorized = mocker.patch('georeference_hsi_pixels.vectorized_georef.process_hsi_line_vectorized')
        
        num_pixels = 5
        hsi_line_idx = 0
        Z_ground_flat_plane_val = 10.0
        # ... (setup hsi_data_list, pose_data_df, sensor_model, lever_arm_params, config as in 1.1, ensuring num_pixels matches) ...
        sensor_model = {'IFOV_x_rad': 0.001, 'IFOV_y_rad': 0.001, 'num_pixels_per_line': num_pixels,
                        'R_sensor_to_body': np.eye(3), 'boresight_vector_sensor': np.array([0,0,1])}
        config = {
            'georeferencing_params': {
                'z_ground_calculation_method': 'flat_plane',
                'Z_ground_flat_plane': Z_ground_flat_plane_val,
                'use_vectorized_processing': True,
                'd_world_z_threshold': 0.1
            },
            'sensor_params': sensor_model,
            'lever_arm': {'main_antenna_lever_arm_body': {'main_x':0,'main_y':0,'main_z':0, 'is_primary':True}}
        }
        hsi_data_list = [{'timestamp': 12345.000, 'data': np.random.rand(num_pixels, 3)}]
        pose_data_df = pd.DataFrame([{'pos_x':0,'pos_y':0,'pos_z':100,'quat_w':1,'quat_x':0,'quat_y':0,'quat_z':0,'timestamp':12345.000}]).set_index('timestamp')


        mock_return_value = []
        expected_nan_count = 0
        for j in range(num_pixels):
            if j % 2 == 0: # Simulate some NaNs
                mock_return_value.append({'hsi_line_index': hsi_line_idx, 'pixel_index': j, 
                                          'X_ground': np.nan, 'Y_ground': np.nan, 'Z_ground': np.nan})
                expected_nan_count += 1
            else:
                mock_return_value.append({'hsi_line_index': hsi_line_idx, 'pixel_index': j, 
                                          'X_ground': float(j), 'Y_ground': float(j+1), 'Z_ground': Z_ground_flat_plane_val})
        mock_process_vectorized.return_value = mock_return_value

        # Act
        # results_data, nan_count_returned = run_georeferencing(hsi_data_list, pose_data_df, sensor_model, lever_arm_params, config)
        # Assuming results_data is a list of dicts or a DataFrame

        # Assert
        # assert nan_count_returned == expected_nan_count
        # assert len(results_data) == num_pixels
        # for idx, res_item in enumerate(results_data): # Adjust if results_data is DataFrame
        #     assert res_item['hsi_line_index'] == hsi_line_idx
        #     assert res_item['pixel_index'] == idx
        #     if idx % 2 == 0:
        #         assert np.isnan(res_item['X_ground'])
        #     else:
        #         assert res_item['X_ground'] == float(idx)
        #         assert res_item['Z_ground'] == Z_ground_flat_plane_val
        pass # Placeholder for actual call and detailed assertions
    ```

## LS6_2: Improve Test Coverage and Performance for `vectorized_georef.py`

### Objective
Significantly increase test coverage for all functions in [`vectorized_georef.py`](vectorized_georef.py:1) to over 70%, ensuring robustness and correctness. Identify and implement performance optimizations.

### Test Cases for functions in `vectorized_georef.py`
(These tests should ideally be in a new file, e.g., `test_vectorized_georef.py`, or a dedicated test class within [`test_georeferencing.py`](test_georeferencing.py:1))

#### Test Case 2.1: `_prepare_rotation_matrices_vectorized`
- **Description**: Verify the correct calculation of rotation matrices from input quaternion components (NumPy arrays).
- **Inputs**:
    - `pose_data`: A dictionary containing `quat_w`, `quat_x`, `quat_y`, `quat_z` as NumPy arrays.
    - Test with known quaternion values: identity, 90-degree rotations about X, Y, Z axes.
    - Test with multiple quaternions in the input arrays.
- **Actions**: Call `_prepare_rotation_matrices_vectorized(pose_data)`.
- **Expected Outputs/Behavior**:
    - Returns a NumPy array of 3x3 rotation matrices, one for each input quaternion.
    - Output matrices match theoretically calculated rotation matrices for the known quaternions.
    - Handles non-unit quaternions by normalizing them internally (or raises `PoseTransformationError` if that's the design).
- **Acceptance Criteria**: Output matrices are numerically close (e.g., `atol=1e-7`) to expected matrices. Correct error handling for invalid inputs.
- **Test Scaffolding (pytest)**:
    ```python
    # In test_vectorized_georef.py
    # from vectorized_georef import _prepare_rotation_matrices_vectorized, PoseTransformationError
    import numpy as np
    import pytest

    def test_prepare_rotation_matrices_vectorized_identity():
        # Arrange
        pose_data = {
            'quat_w': np.array([1.0, 1.0]), 'quat_x': np.array([0.0, 0.0]),
            'quat_y': np.array([0.0, 0.0]), 'quat_z': np.array([0.0, 0.0])
        }
        expected_R_array = np.array([np.eye(3), np.eye(3)])
        # Act
        # R_matrices = _prepare_rotation_matrices_vectorized(pose_data)
        # Assert
        # np.testing.assert_allclose(R_matrices, expected_R_array, atol=1e-7)
        pass # Placeholder

    def test_prepare_rotation_matrices_vectorized_90deg_x_rotation():
        # Arrange: q = [cos(pi/4), sin(pi/4), 0, 0] for 90 deg around X
        sqrt2_inv = 1.0 / np.sqrt(2.0)
        pose_data = {'quat_w': np.array([sqrt2_inv]), 'quat_x': np.array([sqrt2_inv]),
                     'quat_y': np.array([0.0]), 'quat_z': np.array([0.0])}
        expected_R_x_90 = np.array([[[1, 0, 0], [0, 0, -1], [0, 1, 0]]])
        # Act
        # R_matrices = _prepare_rotation_matrices_vectorized(pose_data)
        # Assert
        # np.testing.assert_allclose(R_matrices, expected_R_x_90, atol=1e-7)
        pass # Placeholder
    
    def test_prepare_rotation_matrices_vectorized_non_unit_quaternion_error():
        # Arrange: Non-unit quaternion
        pose_data = {'quat_w': np.array([2.0]), 'quat_x': np.array([0.0]), # Should be normalized or error
                     'quat_y': np.array([0.0]), 'quat_z': np.array([0.0])}
        # Act & Assert
        # with pytest.raises(PoseTransformationError): # Assuming it raises an error for non-unit quats if not normalizing
        #     _prepare_rotation_matrices_vectorized(pose_data)
        # OR if it normalizes, test the output accordingly.
        pass # Placeholder
    ```

#### Test Case 2.2: `_calculate_sensor_pixel_vectors_vectorized`
- **Description**: Verify correct generation of sensor pixel direction vectors in the sensor frame.
- **Inputs**:
    - `num_samples`: Integer, number of pixels per line.
    - `IFOV_x_rad`, `IFOV_y_rad`: Instantaneous fields of view in radians.
    - `boresight_vector_sensor`: NumPy array (3,) for the sensor boresight.
- **Actions**: Call `_calculate_sensor_pixel_vectors_vectorized(...)`.
- **Expected Outputs/Behavior**:
    - Returns a NumPy array of shape (`num_samples`, 3) representing pixel vectors.
    - Vector for the center pixel (if `num_samples` is odd) aligns with `boresight_vector_sensor` (after normalization).
    - Vectors for edge pixels are correctly angled based on IFOV.
    - All returned vectors are normalized.
- **Acceptance Criteria**: Output vectors are numerically close to expected values. Normalization is correct.
- **Test Scaffolding (pytest)**:
    ```python
    # from vectorized_georef import _calculate_sensor_pixel_vectors_vectorized

    def test_calculate_sensor_pixel_vectors_vectorized_boresight_center():
        # Arrange
        num_samples = 3 
        IFOV_x_rad = 0.1
        IFOV_y_rad = 0.1 # Assuming square pixels for simplicity
        boresight_vector_sensor = np.array([0.0, 0.0, 1.0]) # Nadir pointing in sensor frame
        
        # Act
        # pixel_vectors = _calculate_sensor_pixel_vectors_vectorized(num_samples, IFOV_x_rad, IFOV_y_rad, boresight_vector_sensor)
        
        # Assert
        # assert pixel_vectors.shape == (num_samples, 3)
        # center_pixel_idx = num_samples // 2
        # Normalize boresight for comparison if it's not already unit
        # expected_center_vector = boresight_vector_sensor / np.linalg.norm(boresight_vector_sensor)
        # np.testing.assert_allclose(pixel_vectors[center_pixel_idx], expected_center_vector, atol=1e-7)
        # Check normalization of all vectors:
        # norms = np.linalg.norm(pixel_vectors, axis=1)
        # np.testing.assert_allclose(norms, np.ones(num_samples), atol=1e-7)
        pass # Placeholder
    ```

#### Test Case 2.3: `_transform_vectors_to_world_vectorized`
- **Description**: Verify correct transformation of vectors from sensor frame to world frame.
- **Inputs**:
    - `V_sensor`: NumPy array (`num_pixels`, 3) of vectors in sensor frame.
    - `R_body_to_world_line`: NumPy array (`num_poses_for_line`, 3, 3) of rotation matrices (IMU body to world). Typically one per line.
    - `R_sensor_to_body`: Single NumPy array (3,3) for sensor to body rotation.
- **Actions**: Call `_transform_vectors_to_world_vectorized(...)`.
- **Expected Outputs/Behavior**:
    - Returns a NumPy array (`num_pixels`, 3) of vectors transformed to the world frame.
    - Correct transformation for identity rotations and known complex rotations.
- **Acceptance Criteria**: Output vectors are numerically close to expected values.
- **Test Scaffolding (pytest)**:
    ```python
    # from vectorized_georef import _transform_vectors_to_world_vectorized

    def test_transform_vectors_to_world_vectorized_identity_rotations():
        # Arrange
        V_sensor = np.array([[0, 0, 1], [1, 0, 0]]) # Two pixel vectors
        R_body_to_world_line = np.array([np.eye(3)]) # Single identity rotation for the line
        R_sensor_to_body = np.eye(3) # Identity sensor to body
        
        # Act
        # V_world = _transform_vectors_to_world_vectorized(V_sensor, R_body_to_world_line, R_sensor_to_body)
        
        # Assert
        # np.testing.assert_allclose(V_world, V_sensor, atol=1e-7) # Should be unchanged
        pass # Placeholder
    ```

#### Test Case 2.4: `_intersect_rays_with_horizontal_plane_vectorized`
- **Description**: Verify correct calculation of ray intersections with a horizontal plane.
- **Inputs**:
    - `P_sensor_world_expanded`: NumPy array (`num_pixels`, 3) of sensor positions in world frame (origin of rays).
    - `V_world_normalized`: NumPy array (`num_pixels`, 3) of normalized direction vectors in world frame.
    - `Z_ground_flat_plane`: Scalar float, altitude of the horizontal plane.
- **Actions**: Call `_intersect_rays_with_horizontal_plane_vectorized(...)`.
- **Expected Outputs/Behavior**:
    - Returns tuple `(X_ground, Y_ground, Z_ground)`, each a NumPy array (`num_pixels`,).
    - Correct intersection points for nadir pointing rays, oblique rays.
    - Correctly handles rays parallel to the plane (e.g., returns NaNs).
    - Correctly handles rays pointing away from the plane (e.g., returns NaNs).
- **Acceptance Criteria**: Intersection coordinates `X_ground`, `Y_ground` are correct. `Z_ground` matches `Z_ground_flat_plane` for valid intersections. NaN handling is correct.
- **Test Scaffolding (pytest)**:
    ```python
    # from vectorized_georef import _intersect_rays_with_horizontal_plane_vectorized

    def test_intersect_rays_with_horizontal_plane_vectorized_nadir_pointing():
        # Arrange
        P_sensor_world = np.array([[0, 0, 100], [10, 20, 50]]) # Two sensor positions
        V_world_normalized = np.array([[0, 0, -1], [0, 0, -1]]) # Nadir pointing
        Z_ground_flat_plane = 0.0
        
        expected_X_ground = np.array([0.0, 10.0])
        expected_Y_ground = np.array([0.0, 20.0])
        expected_Z_ground = np.array([0.0, 0.0])
        
        # Act
        # X_g, Y_g, Z_g = _intersect_rays_with_horizontal_plane_vectorized(P_sensor_world, V_world_normalized, Z_ground_flat_plane)
        
        # Assert
        # np.testing.assert_allclose(X_g, expected_X_ground, atol=1e-7)
        # np.testing.assert_allclose(Y_g, expected_Y_ground, atol=1e-7)
        # np.testing.assert_allclose(Z_g, expected_Z_ground, atol=1e-7)
        pass # Placeholder

    def test_intersect_rays_with_horizontal_plane_vectorized_ray_parallel_to_plane():
        # Arrange
        P_sensor_world = np.array([[0, 0, 100]])
        V_world_normalized = np.array([[1, 0, 0]]) # Ray pointing along X-axis, parallel to Z=0 plane
        Z_ground_flat_plane = 0.0
        
        # Act
        # X_g, Y_g, Z_g = _intersect_rays_with_horizontal_plane_vectorized(P_sensor_world, V_world_normalized, Z_ground_flat_plane)
        
        # Assert
        # assert np.isnan(X_g[0])
        # assert np.isnan(Y_g[0])
        # assert np.isnan(Z_g[0])
        pass # Placeholder
    ```

#### Test Case 2.5: `process_hsi_line_vectorized` (End-to-End Flat-Plane Path)
- **Description**: Verify the complete flat-plane processing logic within `process_hsi_line_vectorized`.
- **Inputs**:
    - `line_index`, `pose_data` (dictionary with raw pose components as NumPy arrays), `num_samples`, `IFOV_x_rad`, `IFOV_y_rad`, `R_sensor_to_body`, `lever_arm_sensor_body`, `boresight_vector_sensor`.
    - `z_ground_method="flat_plane"`, `z_ground_flat_plane` (scalar).
    - `dsm_interpolator=None`, `dsm_x_coords=None`, `dsm_y_coords=None`.
- **Actions**: Call `process_hsi_line_vectorized(...)`.
- **Expected Outputs/Behavior**:
    - Returns a `List[Dict]`, where each dictionary contains georeferenced coordinates (`X_ground`, `Y_ground`, `Z_ground`, `pixel_index`, `hsi_line_index`) for a pixel.
    - Results match manually calculated or known values for simple scenarios (e.g., nadir view, no lever arm, identity rotations).
    - Handles `PoseTransformationError` if `pose_data` is invalid (e.g., non-unit quaternion if not handled by `_prepare_rotation_matrices_vectorized`).
- **Acceptance Criteria**: Output list of dictionaries is correct. Error handling for pose data is verified.
- **Test Scaffolding (pytest)**:
    ```python
    # from vectorized_georef import process_hsi_line_vectorized, PoseTransformationError

    def test_process_hsi_line_vectorized_flat_plane_nadir_simple():
        # Arrange
        line_idx = 0
        num_pix = 3
        pose = {'pos_x': np.array([0.0]), 'pos_y': np.array([0.0]), 'pos_z': np.array([100.0]),
                  'quat_w': np.array([1.0]), 'quat_x': np.array([0.0]), 
                  'quat_y': np.array([0.0]), 'quat_z': np.array([0.0])}
        # ... (IFOV, R_sensor_to_body=eye, lever_arm=zeros, boresight=[0,0,1]) ...
        # z_ground_flat_plane = 0.0
        
        # Act
        # results = process_hsi_line_vectorized(line_index=line_idx, pose_data=pose, num_samples=num_pix, ...)
        
        # Assert
        # assert len(results) == num_pix
        # center_pixel_res = results[num_pix // 2]
        # np.testing.assert_allclose(center_pixel_res['X_ground'], 0.0, atol=0.1) # Allow tolerance for IFOV spread
        # np.testing.assert_allclose(center_pixel_res['Y_ground'], 0.0, atol=0.1)
        # np.testing.assert_allclose(center_pixel_res['Z_ground'], 0.0, atol=1e-7)
        pass # Placeholder
    ```

#### Test Case 2.6: `process_hsi_line_vectorized` (Error Handling for DSM Path - Incomplete)
- **Description**: Verify behavior when `z_ground_method="dsm_intersection"` is specified but DSM-related parameters (e.g., `dsm_interpolator`) are `None`. This tests robustness before full DSM vectorization.
- **Inputs**:
    - `z_ground_method="dsm_intersection"`.
    - `dsm_interpolator=None`.
    - Other necessary valid parameters for pose, sensor model.
- **Actions**: Call `process_hsi_line_vectorized(...)`.
- **Expected Outputs/Behavior**:
    - The function should handle this gracefully: either raise a specific `VectorizedProcessingError` or `ValueError` indicating missing DSM data for the selected method, or return results with NaN coordinates and log a clear warning. (The exact expected behavior depends on the implemented error handling strategy).
- **Acceptance Criteria**: The defined error handling (exception or NaN results with logging) occurs as expected.
- **Test Scaffolding (pytest)**:
    ```python
    # from vectorized_georef import process_hsi_line_vectorized, VectorizedProcessingError

    def test_process_hsi_line_vectorized_dsm_method_missing_interpolator():
        # Arrange
        # ... (setup valid pose_data, num_samples, sensor params etc.) ...
        # pose = {'pos_x': np.array([0.0]), ..., 'quat_w': np.array([1.0]), ...}

        # Act & Assert
        # with pytest.raises((VectorizedProcessingError, ValueError)): # Or check for NaN results and logged warning
        #     process_hsi_line_vectorized(
        #         line_index=0, pose_data=pose, num_samples=3,
        #         IFOV_x_rad=0.01, IFOV_y_rad=0.01, R_sensor_to_body=np.eye(3),
        #         lever_arm_sensor_body=np.zeros(3), boresight_vector_sensor=np.array([0,0,1]),
        #         z_ground_method="dsm_intersection", # Key
        #         z_ground_flat_plane=None,
        #         dsm_interpolator=None, # Key: missing
        #         dsm_x_coords=None, dsm_y_coords=None,
        #         d_world_z_threshold=0.1 
        #     )
        pass # Placeholder
    ```

#### Test Case 2.7: Benchmarking `process_hsi_line_vectorized` (Flat-Plane)
- **Description**: Measure and establish a baseline performance for `process_hsi_line_vectorized` when using the flat-plane method.
- **Inputs**: Representative `pose_data`, `num_samples` (e.g., 1000-2000 pixels), and other parameters for a typical HSI line.
- **Actions**: Use `pytest-benchmark` to execute `process_hsi_line_vectorized` multiple times.
- **Expected Outputs/Behavior**: Benchmark results (e.g., mean execution time, standard deviation) are recorded.
- **Acceptance Criteria**: The benchmark test completes successfully and provides quantifiable performance metrics. These metrics will serve as a baseline for LS6 and future optimizations.
- **Test Scaffolding (pytest with `pytest-benchmark`)**:
    ```python
    # import pytest # Already imported
    # from vectorized_georef import process_hsi_line_vectorized
    # ... other necessary imports for data setup ...

    @pytest.fixture
    def benchmark_flat_plane_data():
        # Setup and return all necessary inputs for process_hsi_line_vectorized flat-plane call
        line_idx = 0
        num_pix = 1024 # A realistic number of pixels
        pose = {'pos_x': np.random.rand(1) * 1000, 'pos_y': np.random.rand(1) * 1000, 
                  'pos_z': np.random.rand(1) * 500 + 50, # Above ground
                  'quat_w': np.array([1.0]), 'quat_x': np.array([0.0]), 
                  'quat_y': np.array([0.0]), 'quat_z': np.array([0.0])} # Simple orientation
        # Normalize quaternion if necessary for the function
        # norm = np.sqrt(pose['quat_w']**2 + pose['quat_x']**2 + pose['quat_y']**2 + pose['quat_z']**2)
        # pose['quat_w'] /= norm; pose['quat_x'] /= norm; pose['quat_y'] /= norm; pose['quat_z'] /= norm;

        return {
            "line_index": line_idx, "pose_data": pose, "num_samples": num_pix,
            "IFOV_x_rad": 0.0005, "IFOV_y_rad": 0.0005,
            "R_sensor_to_body": np.eye(3), "lever_arm_sensor_body": np.array([0.0, 0.0, 0.0]),
            "boresight_vector_sensor": np.array([0.0, 0.0, 1.0]),
            "z_ground_method": "flat_plane", "z_ground_flat_plane": 0.0,
            "dsm_interpolator": None, "dsm_x_coords": None, "dsm_y_coords": None,
            "d_world_z_threshold": 0.1
        }

    # @pytest.mark.benchmark(group="vectorized_processing") # Optional grouping
    def test_benchmark_process_hsi_line_vectorized_flat_plane(benchmark, benchmark_flat_plane_data):
        # Act
        # result = benchmark(process_hsi_line_vectorized, **benchmark_flat_plane_data)
        # Assert (optional, benchmark itself is the main output)
        # assert result is not None
        # assert len(result) == benchmark_flat_plane_data["num_samples"]
        pass # Placeholder
    ```

## LS6_3: Improve DSM File Path Resolution

### Objective
Ensure that DSM file paths specified in the configuration are resolved robustly, preferably relative to the configuration file's location rather than the current working directory. This addresses Issue 2 from [`reflection_LS5.md:82-85`](reflection_LS5.md:82-85).

### Test Cases for DSM Path Resolution
(These tests will likely target the function responsible for loading DSM data, e.g., `load_dsm_data` in [`georeference_hsi_pixels.py`](georeference_hsi_pixels.py:1), or a utility function it uses for path resolution.)

#### Test Case 3.1: Relative DSM Path Resolution
- **Description**: Verify that a relative DSM path provided in a configuration is correctly resolved relative to the directory of that configuration file.
- **Inputs**:
    - A temporary directory structure: `tmp_dir/config_subdir/test_config.toml` and `tmp_dir/config_subdir/data/dummy.tif`.
    - `test_config.toml` specifies `dsm_file_path = "data/dummy.tif"` (relative path).
    - The path to `test_config.toml` is known.
- **Actions**:
    1. Call the function that loads/resolves the DSM path (e.g., `load_dsm_data` or a helper), providing it the path from the config and the config file's own path.
    2. Mock the actual DSM reading function (e.g., `rasterio.open`) to capture the path it's called with.
- **Expected Outputs/Behavior**:
    - The mocked DSM reading function is called with the correctly resolved absolute path: `tmp_dir/config_subdir/data/dummy.tif`.
- **Acceptance Criteria**: The path passed to the (mocked) DSM reader is the correct, resolved absolute path.
- **Test Scaffolding (pytest)**:
    ```python
    from pathlib import Path
    # import toml # If creating toml file for test
    # from georeference_hsi_pixels import load_dsm_data # Or the relevant function

    def test_dsm_path_resolution_relative_to_config(tmp_path, mocker):
        # Arrange
        config_subdir = tmp_path / "config_location"
        config_subdir.mkdir()
        config_file = config_subdir / "my_config.toml"

        dsm_relative_in_config = "terrain_data/my_dsm.tif"
        
        # Create the expected DSM file location relative to config_file
        expected_dsm_dir = config_subdir / "terrain_data"
        expected_dsm_dir.mkdir()
        expected_dsm_file_abs = expected_dsm_dir / "my_dsm.tif"
        expected_dsm_file_abs.touch() # Create a dummy file

        # This test assumes a function, say `_resolve_dsm_path(path_from_config, config_file_location)`
        # or that `load_dsm_data` itself takes `config_file_location` for resolution.
        # For this example, let's assume `load_dsm_data` is modified or calls such a helper.
        
        mock_rasterio_open = mocker.patch('rasterio.open') # Or the specific DSM opening function used

        # Act: Call the function that should perform the resolution and attempt to open.
        # This call depends on the actual signature of load_dsm_data or helper.
        # Example:
        # try:
        #     load_dsm_data(
        #         dsm_file_path_from_config=dsm_relative_in_config,
        #         # This new/modified param indicates where the config file itself is located
        #         config_actual_path=str(config_file) 
        #         # ... other necessary args for load_dsm_data ...
        #     )
        # except Exception: # To catch errors if rasterio.open isn't fully mocked for return
        #     pass 

        # Assert
        # mock_rasterio_open.assert_called_once()
        # called_path_arg = mock_rasterio_open.call_args[0][0]
        # assert Path(called_path_arg).resolve() == expected_dsm_file_abs.resolve()
        pass # Placeholder
    ```

#### Test Case 3.2: Absolute DSM Path Resolution
- **Description**: Verify that if an absolute DSM path is provided in the configuration, it is used directly without modification.
- **Inputs**:
    - A temporary directory structure: `tmp_dir/another_data_dir/absolute_dummy.tif`.
    - `test_config.toml` (location doesn't strictly matter for this test if path is absolute) specifies `dsm_file_path = "/path/to/tmp_dir/another_data_dir/absolute_dummy.tif"` (the actual absolute path).
- **Actions**:
    1. Call the DSM loading/resolving function.
    2. Mock the DSM reading function.
- **Expected Outputs/Behavior**:
    - The mocked DSM reading function is called with the exact absolute path specified in the config.
- **Acceptance Criteria**: The path passed to the (mocked) DSM reader is identical to the absolute path from the config.
- **Test Scaffolding (pytest)**:
    ```python
    def test_dsm_path_resolution_absolute_path_in_config(tmp_path, mocker):
        # Arrange
        config_file = tmp_path / "some_config.toml" # Location of config file itself
        
        # Create a dummy DSM file at an "absolute" path (within tmp_path for testability)
        abs_dsm_data_dir = tmp_path / "external_dsm_location"
        abs_dsm_data_dir.mkdir()
        dsm_absolute_path_in_config = (abs_dsm_data_dir / "global_dsm.tif").resolve()
        dsm_absolute_path_in_config.touch()

        mock_rasterio_open = mocker.patch('rasterio.open')

        # Act
        # try:
        #     load_dsm_data(
        #         dsm_file_path_from_config=str(dsm_absolute_path_in_config),
        #         config_actual_path=str(config_file) 
        #         # ... other args ...
        #     )
        # except Exception:
        #     pass

        # Assert
        # mock_rasterio_open.assert_called_once()
        # called_path_arg = mock_rasterio_open.call_args[0][0]
        # assert Path(called_path_arg).resolve() == dsm_absolute_path_in_config.resolve()
        pass # Placeholder
    ```

#### Test Case 3.3: Non-Existent Relative DSM Path (After Resolution)
- **Description**: Verify that if a relative DSM path is correctly resolved (relative to config) but the target file does not exist at that resolved location, the appropriate `FileNotFoundError` (or `rasterio.errors.RasterioIOError`) is raised by the underlying file access attempt.
- **Inputs**:
    - `test_config.toml` in `tmp_dir/config_subdir/`.
    - Config specifies `dsm_file_path = "data/non_existent.tif"`.
    - The file `tmp_dir/config_subdir/data/non_existent.tif` does *not* exist.
- **Actions**: Call the DSM loading/resolving function. (Do not mock `rasterio.open` if testing its error, or mock it to raise `FileNotFoundError` if called with the specific resolved path).
- **Expected Outputs/Behavior**: A `FileNotFoundError` or `rasterio.errors.RasterioIOError` is raised.
- **Acceptance Criteria**: The correct file-not-found type error is raised, indicating the path resolution logic itself didn't mask the issue but passed the (correctly resolved but non-existent) path to the file opener.
- **Test Scaffolding (pytest)**:
    ```python
    # import rasterio # For specific error type
    # import pytest # For raises

    def test_dsm_path_resolution_relative_file_not_found_at_resolved_path(tmp_path, mocker):
        # Arrange
        config_subdir = tmp_path / "config_location"
        config_subdir.mkdir()
        config_file = config_subdir / "my_config.toml"

        dsm_relative_in_config = "terrain_data/ghost_dsm.tif" # This file won't exist

        # The directory where it would be, might exist or not, doesn't matter as file is missing
        # Path(config_subdir / "terrain_data").mkdir(exist_ok=True) 
        
        # Act & Assert
        # with pytest.raises((FileNotFoundError, rasterio.errors.RasterioIOError)): # Adjust as per actual error
        #     load_dsm_data(
        #         dsm_file_path_from_config=dsm_relative_in_config,
        #         config_actual_path=str(config_file)
        #         # ... other args ...
        #     )
        pass # Placeholder