## Prompt [LS8_1]

### Context
[`reflection_LS7.md:15-25`](reflection_LS7.md:15-25) (Issue 1) identified that the `calculate_dsm_intersections_vectorized` function in [`vectorized_georef.py:156-295`](vectorized_georef.py:156-295) is not fully vectorized. It still contains a per-ray loop ([`vectorized_georef.py:201`](vectorized_georef.py:201)), which hinders performance for DSM processing. The goal of LS7_2 ([`prompts_LS7.md:53-56`](prompts_LS7.md:53-56)) was full vectorization.

### Objective
Refactor the `calculate_dsm_intersections_vectorized` function in [`vectorized_georef.py`](vectorized_georef.py) to eliminate the explicit per-ray loop and perform DSM intersection calculations vectorially across all rays simultaneously.

### Focus Areas
- Generate all coarse sample points for *all* rays in a single vectorized operation.
- Perform bounds checking for all sample points vectorially.
- Query the DSM interpolator with all valid sample point coordinates in a batch (adapting interaction if necessary).
- Vectorize the sign change detection and bisection refinement steps across all relevant rays.

### Code Reference
[`vectorized_georef.py:156-295`](vectorized_georef.py:156-295)

### Requirements
- Eliminate the `for ray_idx in range(num_rays):` loop currently at [`vectorized_georef.py:201`](vectorized_georef.py:201).
- The refactored function must maintain or improve accuracy compared to the previous (partially vectorized) version.
- The solution must be robust and correctly handle edge cases (e.g., no intersection for some/all rays, rays outside DSM boundaries).
- Ensure efficient memory usage, especially with a large number of rays.

### Expected Improvements
- Significant performance improvement for DSM-based georeferencing, noticeable in `process_hsi_line_vectorized` when using DSM.
- Reduction in the algorithmic complexity of the iteration logic.
- An increase in the `performance.scalability_score` in subsequent scoring.

## Prompt [LS8_2]

### Context
[`reflection_LS7.md:26-37`](reflection_LS7.md:26-37) (Issue 2) and [`scores_LS7.json:43`](scores_LS7.json:43) highlight that test coverage for [`vectorized_georef.py`](vectorized_georef.py) is only 67%, falling short of the 85% target ([`scores_LS7.json:24`](scores_LS7.json:24)). A critical test, `test_process_hsi_line_vectorized_dsm_correctness` (specified in [`test_specs_LS7.md:304`](test_specs_LS7.md:304)), is missing. Additionally, [`reflection_LS7.md:31-32`](reflection_LS7.md:31-32) noted a potential parameter mismatch for `calculate_sensor_view_vectors_vectorized` tests, with a recommendation to update specs and implement tests in LS7 ([`reflection_LS7.md:35`](reflection_LS7.md:35)).

### Objective
Increase test coverage for [`vectorized_georef.py`](vectorized_georef.py) to at least 85% by implementing the missing `test_process_hsi_line_vectorized_dsm_correctness`, ensuring tests for `calculate_sensor_view_vectors_vectorized` align with its signature, and adding comprehensive tests for other public helper functions.

### Focus Areas
- Implementation of `test_process_hsi_line_vectorized_dsm_correctness`.
- Comprehensive testing of `calculate_sensor_view_vectors_vectorized`.
- Comprehensive testing of `transform_to_world_coordinates_vectorized`.
- Comprehensive testing of `calculate_flat_plane_intersections_vectorized`.

### Code Reference
- Implementation: [`vectorized_georef.py`](vectorized_georef.py)
- Tests: Primarily in [`test_georeferencing.py`](test_georeferencing.py) (or a new `test_vectorized_georef.py` if deemed more appropriate).
- Test Specifications: [`test_specs_LS7.md`](test_specs_LS7.md) (especially [`test_specs_LS7.md:304-357`](test_specs_LS7.md:304-357) for the DSM correctness test).

### Requirements
- Implement `test_process_hsi_line_vectorized_dsm_correctness` in [`test_georeferencing.py`](test_georeferencing.py), ensuring it validates the fully vectorized DSM intersection logic developed in response to Prompt LS8_1. Refer to specifications in [`test_specs_LS7.md:304-357`](test_specs_LS7.md:304-357).
- Verify if test specifications for `calculate_sensor_view_vectors_vectorized` ([`vectorized_georef.py:37-43`](vectorized_georef.py:37-43)) were updated in LS7. If not, update the specifications in `test_specs_LS8.md` to match its current signature: `(pixel_indices, vinkelx_rad_all, vinkely_rad_all, scale_vinkel_x, offset_vinkel_x)`. Implement comprehensive tests based on the correct signature.
- Add comprehensive unit tests for `transform_to_world_coordinates_vectorized` and `calculate_flat_plane_intersections_vectorized`, covering valid inputs, edge cases (e.g., zero vectors, empty inputs), and error conditions.
- All new tests must follow the Arrange-Act-Assert (AAA) pattern and include clear docstrings.

### Expected Improvements
- Line coverage for [`vectorized_georef.py`](vectorized_georef.py) to reach at least 85%.
- Increased confidence in the correctness and robustness of all vectorized georeferencing helper functions.
- `scores_LS{n}.json:coverage.vectorized_georef_line_coverage` to be >= 85.

## Prompt [LS8_3]

### Context
[`reflection_LS7.md:58-64`](reflection_LS7.md:58-64) (Issue 4) identified that the module [`compare_timestamps.py`](compare_timestamps.py) lacks a dedicated test suite. This is a gap in achieving the overall project test coverage target of 50%.

### Objective
Create a new test suite, `test_compare_timestamps.py`, to provide comprehensive test coverage for the functionalities within [`compare_timestamps.py`](compare_timestamps.py).

### Focus Areas
- Testing the `parse_haip_timestamp` function ([`compare_timestamps.py:5-14`](compare_timestamps.py:5-14)).
- Testing the core logic of the `main` function ([`compare_timestamps.py:16-75`](compare_timestamps.py:16-75)), including file interactions and data processing.

### Code Reference
- Implementation: [`compare_timestamps.py`](compare_timestamps.py)
- New Test File: `test_compare_timestamps.py`

### Requirements
- Create the new test file `test_compare_timestamps.py`.
- Implement test cases for `parse_haip_timestamp` covering:
    - Valid HAIP files and correct timestamp extraction.
    - HAIP files missing the 'rgb:' key (should handle gracefully or raise specific error).
    - File read errors (e.g., file not found, permission issues), ensuring appropriate exceptions are raised or handled.
- Implement test cases for the `main` function, focusing on its logic by:
    - Mocking file system interactions (`os.listdir`, `open`, `os.path.join`, `os.path.exists`).
    - Mocking `parse_haip_timestamp` to control its output during tests.
    - Verifying correct parsing of `shots.geojson` (mocked content).
    - Testing the matching logic between `shots.geojson` entries and HAIP file data.
    - Verifying correct calculation and reporting of time differences.
    - Testing scenarios with no matching files, empty directories, and invalid geojson structures.
- All new tests must follow the AAA pattern and include clear docstrings.

### Expected Improvements
- Substantial test coverage for [`compare_timestamps.py`](compare_timestamps.py).
- Increased reliability of the timestamp comparison utility.
- Contribution towards achieving the overall project test coverage target of 50%.

## Prompt [LS8_4]

### Context
[`reflection_LS7.md:58-60`](reflection_LS7.md:58-60) and [`reflection_LS7.md:65-69`](reflection_LS7.md:65-69) (Issue 4) identified that [`create_georeferenced_rgb.py`](create_georeferenced_rgb.py) also lacks a dedicated test suite, impacting overall project coverage. This module is crucial for generating GeoTIFF outputs.

### Objective
Create a new test suite, `test_create_georeferenced_rgb.py`, to provide comprehensive test coverage for the functionalities within [`create_georeferenced_rgb.py`](create_georeferenced_rgb.py).

### Focus Areas
- Testing `find_nearest_band_index` ([`create_georeferenced_rgb.py:14-18`](create_georeferenced_rgb.py:14-18)).
- Testing `normalize_band` ([`create_georeferenced_rgb.py:20-56`](create_georeferenced_rgb.py:20-56)).
- Testing the core logic of `run_create_rgb_geotiff` ([`create_georeferenced_rgb.py:58-268`](create_georeferenced_rgb.py:58-268)).

### Code Reference
- Implementation: [`create_georeferenced_rgb.py`](create_georeferenced_rgb.py)
- New Test File: `test_create_georeferenced_rgb.py`

### Requirements
- Create the new test file `test_create_georeferenced_rgb.py`.
- Implement test cases for `find_nearest_band_index` covering:
    - Exact wavelength match.
    - Nearest wavelength match (lower and upper).
    - Empty list of available wavelengths.
    - Target wavelength outside the range of available wavelengths.
- Implement test cases for `normalize_band` covering:
    - "min_max" normalization method.
    - "percentile_2_98" normalization method.
    - Correct handling of `no_data_val` (ignoring it during min/max/percentile calculation, preserving it in output).
    - Handling of bands containing NaN values.
    - Scenarios where the entire band is `no_data_val` or NaN.
    - Input band data types (e.g., float, integer).
- Implement test cases for `run_create_rgb_geotiff`, focusing on its logic by:
    - Mocking HSI data loading (e.g., `spectral.open_image` and its methods like `read_band`, `read_subimage`).
    - Mocking georeferenced pixels CSV reading (e.g., `pd.read_csv`).
    - Mocking GeoTIFF writing (e.g., `rasterio.open` in write mode and dataset methods).
    - Verifying correct band selection using `find_nearest_band_index` for R, G, B.
    - Testing the resampling/interpolation logic (e.g., by mocking `scipy.interpolate.griddata` or `KDTree` if used, or providing minimal valid mocked data for interpolation).
    - Verifying the correct application of normalization methods via `normalize_band`.
    - Ensuring correct GeoTIFF metadata (transform, CRS, band count, data types, nodata value) is prepared and passed to the writer.
    - Handling of missing input files or invalid data formats.
- All new tests must follow the AAA pattern and include clear docstrings.

### Expected Improvements
- Substantial test coverage for [`create_georeferenced_rgb.py`](create_georeferenced_rgb.py).
- Increased reliability of the GeoTIFF generation process.
- Contribution towards achieving the overall project test coverage target of 50%.

## Prompt [LS8_5]

### Context
The overall project line coverage is currently estimated at 40% ([`scores_LS7.json:41`](scores_LS7.json:41)), which is below the target of 50% ([`scores_LS7.json:23`](scores_LS7.json:23)). While Prompts LS8_2, LS8_3, and LS8_4 address specific modules, this prompt ensures that broader efforts are made to meet the overall coverage goal. [`reflection_LS7.md:38-56`](reflection_LS7.md:38-56) (Issue 3) also highlighted incomplete test coverage for `load_hsi_data` and `load_webodm_data` in [`synchronize_hsi_webodm.py`](synchronize_hsi_webodm.py), which currently has an estimated coverage of 70% ([`scores_LS7.json:45`](scores_LS7.json:45)).

### Objective
Increase the overall project test coverage to at least 50% by strategically adding tests to modules that will contribute effectively to this target, with a specific focus on completing tests for [`synchronize_hsi_webodm.py`](synchronize_hsi_webodm.py) as previously recommended.

### Focus Areas
- Completing test coverage for `load_hsi_data` and `load_webodm_data` in [`synchronize_hsi_webodm.py`](synchronize_hsi_webodm.py).
- Identifying and testing other critical, under-tested modules or functions if the 50% target is still not met after addressing the above and other LS8 prompts.

### Code Reference
- Primarily [`synchronize_hsi_webodm.py`](synchronize_hsi_webodm.py) and its test file [`test_synchronize_hsi_webodm.py`](test_synchronize_hsi_webodm.py).
- Other project modules as identified by coverage analysis.

### Requirements
- In [`test_synchronize_hsi_webodm.py`](test_synchronize_hsi_webodm.py):
    - Add/complete a test class `TestLoadHsiData` with comprehensive test cases for `load_hsi_data` ([`synchronize_hsi_webodm.py:87-164`](synchronize_hsi_webodm.py:87-164)) covering:
        - Valid sync file parsing and correct data extraction (timestamps, line numbers).
        - Handling of file not found (should raise `InputDataError`).
        - Invalid sync file format (e.g., missing header, incorrect column count) raising `HSIDataError`.
        - Empty sync file raising `HSIDataError`.
        - Correct reordering of line numbers.
        - Correct conversion of timestamps.
        - Logging of warning for mismatch between sync file lines and HDR lines.
    - Add/complete a test class `TestLoadWebodmData` with comprehensive test cases for `load_webodm_data` ([`synchronize_hsi_webodm.py:167-237`](synchronize_hsi_webodm.py:167-237)) covering:
        - Valid WebODM CSV parsing and correct data extraction.
        - Handling of file not found (should raise `InputDataError`).
        - Empty CSV file (should raise `InputDataError`).
        - Missing required columns (e.g., 'filename', 'timestamp', 'x', 'y', 'z') raising `SynchronizationError`.
        - Data conversion errors for numeric fields (e.g., non-numeric timestamp, coordinates) leading to warnings and row skipping.
- After implementing tests from other LS8 prompts and the `synchronize_hsi_webodm.py` tests above, run a coverage analysis.
- If overall coverage is still below 50%, identify other modules/functions that are critical and under-tested. Add targeted tests to these areas to reach the 50% goal.
- All new tests must follow the AAA pattern and include clear docstrings.

### Expected Improvements
- Overall project line coverage to be >= 50%.
- Test coverage for [`synchronize_hsi_webodm.py`](synchronize_hsi_webodm.py) to be comprehensive for its data loading functions.
- Increased stability and reliability of the entire data processing pipeline.
- `scores_LS{n}.json:coverage.overall_line_coverage_reported_estimate` to be >= 50.
- `scores_LS{n}.json:coverage.synchronize_hsi_webodm_estimated_coverage` to improve.