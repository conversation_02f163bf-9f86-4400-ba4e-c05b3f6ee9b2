# Test Specifications for Layer LS2

This document outlines the test cases, expected behaviors, acceptance criteria, and scaffolding for the improvements and features detailed in [`prompts_LS2.md`](prompts_LS2.md).

## Based on Prompt [LS2_1]: Lever Arm Correction

**Objective**: Ensure accurate lever arm usage, prioritizing HSI header data with a clear configuration override mechanism.

### Test Case 1.1: Lever Arm from HDR Only
-   **Description**: Verify that `lever_arm_from_hdr` is used when `config.toml` provides no override or zeroed values.
-   **Module**: [`georeference_hsi_pixels.py`](georeference_hsi_pixels.py:1)
-   **Function(s) to Test**: Lever arm selection logic (e.g., within `run_georeferencing` or a dedicated helper).
-   **Inputs**:
    -   Mock HSI header data: `lever_arm_from_hdr = (0.1, 0.05, -0.02)`
    -   Mock `config.toml` data: `lever_arm` section either missing, or `x=0.0, y=0.0, z=0.0`.
-   **Actions**:
    -   Initialize and run the georeferencing process for a sample HSI line.
-   **Expected Outputs/Behavior**:
    -   The `effective_lever_arm_body` used in calculations is `(0.1, 0.05, -0.02)`.
    -   No warning regarding zero lever arm is logged.
-   **Acceptance Criteria**: The final georeferenced coordinates are consistent with using the HDR lever arm. The `effective_lever_arm_body` matches the HDR values.
-   **Test Scaffolding (Python/pytest)**:
    ```python
    import numpy as np
    # Assuming georeference_hsi_pixels module and relevant functions/classes
    # from georeference_hsi_pixels import determine_effective_lever_arm # or similar

    def test_lever_arm_hdr_only(mocker):
        # Arrange
        hdr_lever_arm = np.array([0.1, 0.05, -0.02])
        config_lever_arm = np.array([0.0, 0.0, 0.0]) # Or None if config key is absent
        
        # Mock the functions/objects that provide these values
        mocker.patch('georeference_hsi_pixels.parse_hsi_header', return_value={'lever_arm': hdr_lever_arm})
        mocker.patch('georeference_hsi_pixels.load_config_lever_arm', return_value=config_lever_arm) # Example
        
        # Act
        # effective_arm = determine_effective_lever_arm(hdr_lever_arm, config_lever_arm) # Example call
        # For a more integrated test, run a simplified georeferencing step
        # and inspect the lever arm used internally or the output.
        
        # Assert
        # np.testing.assert_array_almost_equal(effective_arm, hdr_lever_arm)
        # Assert no warning was logged (using caplog fixture if logging is tested here)
        pass
    ```

### Test Case 1.2: Lever Arm from Config Override
-   **Description**: Verify that `config.toml` lever arm values override `lever_arm_from_hdr` when explicitly set and non-zero.
-   **Module**: [`georeference_hsi_pixels.py`](georeference_hsi_pixels.py:1)
-   **Function(s) to Test**: Lever arm selection logic.
-   **Inputs**:
    -   Mock HSI header data: `lever_arm_from_hdr = (0.1, 0.05, -0.02)`
    -   Mock `config.toml` data: `lever_arm` section with `x=0.15, y=0.07, z=-0.03` (non-zero).
-   **Actions**:
    -   Initialize and run the georeferencing process.
-   **Expected Outputs/Behavior**:
    -   The `effective_lever_arm_body` used is `(0.15, 0.07, -0.03)`.
-   **Acceptance Criteria**: Georeferencing uses the config lever arm. `effective_lever_arm_body` matches config values.
-   **Test Scaffolding (Python/pytest)**:
    ```python
    def test_lever_arm_config_override(mocker):
        # Arrange
        hdr_lever_arm = np.array([0.1, 0.05, -0.02])
        config_lever_arm = np.array([0.15, 0.07, -0.03])
        
        mocker.patch('georeference_hsi_pixels.parse_hsi_header', return_value={'lever_arm': hdr_lever_arm})
        mocker.patch('georeference_hsi_pixels.load_config_lever_arm', return_value=config_lever_arm)
        
        # Act
        # effective_arm = determine_effective_lever_arm(hdr_lever_arm, config_lever_arm)
        
        # Assert
        # np.testing.assert_array_almost_equal(effective_arm, config_lever_arm)
        pass
    ```

### Test Case 1.3: Lever Arm from HDR, Zero Config
-   **Description**: Verify `lever_arm_from_hdr` is used if `config.toml` provides zeroed values (which should not count as an explicit override).
-   **Module**: [`georeference_hsi_pixels.py`](georeference_hsi_pixels.py:1)
-   **Function(s) to Test**: Lever arm selection logic.
-   **Inputs**:
    -   Mock HSI header data: `lever_arm_from_hdr = (0.1, 0.05, -0.02)`
    -   Mock `config.toml` data: `lever_arm` section with `x=0.0, y=0.0, z=0.0`.
-   **Actions**:
    -   Initialize and run the georeferencing process.
-   **Expected Outputs/Behavior**:
    -   The `effective_lever_arm_body` used is `(0.1, 0.05, -0.02)`.
-   **Acceptance Criteria**: Georeferencing uses the HDR lever arm.
-   **Test Scaffolding (Python/pytest)**:
    ```python
    def test_lever_arm_hdr_with_zero_config(mocker):
        # Arrange
        hdr_lever_arm = np.array([0.1, 0.05, -0.02])
        config_lever_arm = np.array([0.0, 0.0, 0.0])
        
        mocker.patch('georeference_hsi_pixels.parse_hsi_header', return_value={'lever_arm': hdr_lever_arm})
        mocker.patch('georeference_hsi_pixels.load_config_lever_arm', return_value=config_lever_arm)
        
        # Act
        # effective_arm = determine_effective_lever_arm(hdr_lever_arm, config_lever_arm)
        
        # Assert
        # np.testing.assert_array_almost_equal(effective_arm, hdr_lever_arm)
        pass
    ```

### Test Case 1.4: Both HDR and Config Lever Arm are Zero
-   **Description**: Verify behavior when both sources provide zero lever arm. A warning should be logged.
-   **Module**: [`georeference_hsi_pixels.py`](georeference_hsi_pixels.py:1)
-   **Function(s) to Test**: Lever arm selection logic and warning generation.
-   **Inputs**:
    -   Mock HSI header data: `lever_arm_from_hdr = (0.0, 0.0, 0.0)`
    -   Mock `config.toml` data: `lever_arm` section with `x=0.0, y=0.0, z=0.0`.
-   **Actions**:
    -   Initialize and run the georeferencing process.
-   **Expected Outputs/Behavior**:
    -   The `effective_lever_arm_body` used is `(0.0, 0.0, 0.0)`.
    -   A warning is logged indicating a zero lever arm is being used.
-   **Acceptance Criteria**: `effective_lever_arm_body` is zero, and the specified warning is present in logs.
-   **Test Scaffolding (Python/pytest)**:
    ```python
    def test_lever_arm_both_zero(mocker, caplog):
        # Arrange
        hdr_lever_arm = np.array([0.0, 0.0, 0.0])
        config_lever_arm = np.array([0.0, 0.0, 0.0])
        
        mocker.patch('georeference_hsi_pixels.parse_hsi_header', return_value={'lever_arm': hdr_lever_arm})
        mocker.patch('georeference_hsi_pixels.load_config_lever_arm', return_value=config_lever_arm)
        
        # Act
        # effective_arm = determine_effective_lever_arm(hdr_lever_arm, config_lever_arm)
        # georeference_module.process_with_lever_arm(...) # Trigger logging
        
        # Assert
        # np.testing.assert_array_almost_equal(effective_arm, np.array([0.0, 0.0, 0.0]))
        # assert "Warning: Effective lever arm is (0,0,0)" in caplog.text # Or similar message
        pass
    ```

### Test Case 1.5: Warning for Zero Effective Arm with Non-Zero HDR
-   **Description**: Verify warning if `effective_lever_arm_body` is `(0,0,0)` (due to config explicitly setting it to zero, which is not an override in this new logic, or some other misconfiguration) while `lever_arm_from_hdr` was non-zero and not overridden by a *non-zero* config value. This case might be subtle depending on final logic: if config `(0,0,0)` means "use HDR", then this warning might only trigger if HDR was non-zero and somehow the final arm became zero *without* a valid non-zero config override. The prompt says "if the `effective_lever_arm_body` used in calculations is `(0,0,0)` while `lever_arm_from_hdr` was non-zero and not overridden by a non-zero config value."
-   **Module**: [`georeference_hsi_pixels.py`](georeference_hsi_pixels.py:1)
-   **Function(s) to Test**: Lever arm selection logic and warning generation.
-   **Inputs**:
    -   Mock HSI header data: `lever_arm_from_hdr = (0.1, 0.05, -0.02)`
    -   Mock `config.toml` data: `lever_arm` is NOT set or is `(0,0,0)`.
    -   And somehow, the logic results in `effective_lever_arm_body = (0,0,0)` (this scenario needs careful setup to be valid based on the implemented logic).
-   **Actions**:
    -   Initialize and run the georeferencing process.
-   **Expected Outputs/Behavior**:
    -   A specific warning is logged: "Effective lever arm is (0,0,0) but HSI header reported a non-zero lever arm [values] which was not overridden by a non-zero configuration. Georeferencing accuracy may be compromised."
-   **Acceptance Criteria**: The specific warning message is logged.
-   **Test Scaffolding (Python/pytest)**:
    ```python
    def test_warning_zero_effective_arm_with_nonzero_hdr(mocker, caplog):
        # Arrange
        hdr_lever_arm = np.array([0.1, 0.05, -0.02])
        # This case implies that despite a non-zero HDR arm and no non-zero config override,
        # the effective arm became zero. This might indicate an issue elsewhere or a specific
        # interpretation of how config (0,0,0) interacts.
        # For the test, we'd mock the outcome of lever arm determination if direct,
        # or ensure the conditions leading to it.
        
        # Example: Assume a function that finalizes the lever arm
        mocker.patch('georeference_hsi_pixels.get_final_lever_arm_and_log_warnings', 
                       return_value=(np.array([0.0, 0.0, 0.0]), hdr_lever_arm, False)) # False meaning no valid non-zero override
        
        # Act
        # georeference_module.some_function_that_uses_lever_arm()
        
        # Assert
        # assert "Effective lever arm is (0,0,0) but HSI header reported a non-zero lever arm" in caplog.text
        # assert str(hdr_lever_arm) in caplog.text
        pass
    ```

### Test Case 1.6: Comment Update in `synchronize_hsi_webodm.py`
-   **Description**: Verify the misleading comment about lever arm correction is updated or removed.
-   **Module**: [`synchronize_hsi_webodm.py`](synchronize_hsi_webodm.py:1)
-   **Function(s) to Test**: N/A (Code/Comment Inspection).
-   **Inputs**: N/A.
-   **Actions**: Inspect the code file [`synchronize_hsi_webodm.py`](synchronize_hsi_webodm.py:323) (lines 323-329).
-   **Expected Outputs/Behavior**: The old comment "Lever arm correction not implemented here yet" (or similar) is removed or replaced with accurate information.
-   **Acceptance Criteria**: The specified lines no longer contain the misleading comment.
-   **Test Scaffolding**: Manual inspection or a script that greps the file (though typically not part of unit tests).
    ```python
    # This is more of a static check, can be part of a linting/review process.
    # For TDD, the "test" is the requirement to change it.
    # A test could read the file and check, but it's unusual for unit tests.
    def test_comment_updated_in_synchronize_script():
        # Arrange
        file_path = "synchronize_hsi_webodm.py"
        misleading_comment_substring = "Lever arm correction not implemented here yet" # Example
        
        # Act
        with open(file_path, 'r') as f:
            content = f.read()
            
        # Assert
        # assert misleading_comment_substring not in content[relevant_section]
        pass # Placeholder for a more robust check if desired
    ```

### Test Case 1.7: Documentation Update in `main_pipeline_documentation.md`
-   **Description**: Verify documentation clearly states the new lever arm source priority.
-   **Module**: [`main_pipeline_documentation.md`](main_pipeline_documentation.md:1)
-   **Function(s) to Test**: N/A (Documentation Inspection).
-   **Inputs**: N/A.
-   **Actions**: Inspect [`main_pipeline_documentation.md`](main_pipeline_documentation.md:56) (around line 56).
-   **Expected Outputs/Behavior**: Documentation accurately reflects: HDR first, then explicit non-zero config override.
-   **Acceptance Criteria**: The documentation is clear and correct regarding lever arm logic.
-   **Test Scaffolding**: Manual inspection.

## Based on Prompt [LS2_2]: Vectorization for Performance

**Objective**: Significantly improve georeferencing processing speed by vectorizing calculations.

### Test Case 2.1: Vectorized `d_sensor_frame` and `d_world` Calculation Correctness
-   **Description**: Verify that vectorized calculations for `d_sensor_frame` and `d_world` produce the same results as the original iterative method.
-   **Module**: [`georeference_hsi_pixels.py`](georeference_hsi_pixels.py:1)
-   **Function(s) to Test**: Refactored main processing loop, specifically `d_sensor_frame` and `d_world` calculations.
-   **Inputs**:
    -   Sample HSI line data (pixel indices).
    -   Sensor parameters (IFOV, image width).
    -   Rotation matrix `R_sensor_to_world`.
-   **Actions**:
    -   Calculate `d_world` using both the old (iterative, if preserved for testing) and new (vectorized) methods for a full line of pixels.
-   **Expected Outputs/Behavior**:
    -   The array of `d_world` vectors from the vectorized method is numerically close (within a small tolerance) to the `d_world` vectors produced by iterating and calculating one by one.
-   **Acceptance Criteria**: `np.allclose(d_world_vectorized, d_world_iterative)` is true.
-   **Test Scaffolding (Python/pytest)**:
    ```python
    def test_vectorized_d_world_correctness(sample_hsi_line_data, sensor_params, R_sensor_to_world):
        # Arrange
        # Old iterative function (if available for comparison)
        # def calculate_d_world_iterative(pixel_idx, sensor_params, R_sensor_to_world): ...
        
        # New vectorized function
        # def calculate_d_world_vectorized(pixel_indices, sensor_params, R_sensor_to_world): ...
        
        pixel_indices = np.arange(sample_hsi_line_data['width'])
        
        # Act
        # d_world_iter = np.array([calculate_d_world_iterative(px, ...) for px in pixel_indices])
        # d_world_vec = calculate_d_world_vectorized(pixel_indices, ...)
        
        # Assert
        # np.testing.assert_allclose(d_world_vec, d_world_iter, rtol=1e-6)
        pass
    ```

### Test Case 2.2: Vectorized Flat-Plane Intersection Correctness
-   **Description**: Verify vectorized flat-plane intersection calculations match iterative results.
-   **Module**: [`georeference_hsi_pixels.py`](georeference_hsi_pixels.py:1)
-   **Function(s) to Test**: Refactored flat-plane intersection logic.
-   **Inputs**:
    -   Array of `P_sensor_world` (sensor positions).
    -   Array of `d_world` (ray vectors).
    -   `z_ground` (ground elevation).
    -   `z_ground_method = "flat_plane"`.
-   **Actions**:
    -   Calculate intersection points using both old and new methods.
-   **Expected Outputs/Behavior**: Intersection points match.
-   **Acceptance Criteria**: `np.allclose(intersections_vectorized, intersections_iterative)` is true.
-   **Test Scaffolding (Python/pytest)**:
    ```python
    def test_vectorized_flat_plane_intersection_correctness(P_sensor_world_array, d_world_array, z_ground):
        # Arrange
        # Old iterative function: def calculate_flat_plane_intersection_iterative(P_sensor, d_world, z_g): ...
        # New vectorized function: def calculate_flat_plane_intersection_vectorized(P_sensors, d_worlds, z_g): ...
        
        # Act
        # intersections_iter = np.array([calculate_flat_plane_intersection_iterative(P_sensor_world_array[i], d_world_array[i], z_ground) for i in range(len(P_sensor_world_array))])
        # intersections_vec = calculate_flat_plane_intersection_vectorized(P_sensor_world_array, d_world_array, z_ground)
        
        # Assert
        # np.testing.assert_allclose(intersections_vec, intersections_iter, rtol=1e-6)
        pass
    ```

### Test Case 2.3: `calculate_ray_dsm_intersection` Optimization Correctness
-   **Description**: If `calculate_ray_dsm_intersection` is partially vectorized or batched, verify its output matches the original for various scenarios.
-   **Module**: [`georeference_hsi_pixels.py`](georeference_hsi_pixels.py:114)
-   **Function(s) to Test**: `calculate_ray_dsm_intersection`.
-   **Inputs**:
    -   Sample rays (origin, direction).
    -   Mock DSM data (e.g., a simple `rasterio.DatasetReader` mock).
    -   Scenarios: ray hits DSM, ray misses DSM, ray starts below DSM.
-   **Actions**:
    -   Run the (potentially optimized) function.
-   **Expected Outputs/Behavior**: Intersection points (or lack thereof) are correct.
-   **Acceptance Criteria**: Output matches expected intersection points/status for the given mock DSM.
-   **Test Scaffolding (Python/pytest)**:
    ```python
    # Mocking rasterio and DSM interaction is key here
    class MockDSM:
        def __init__(self, data, transform):
            self.data = data # NumPy array
            self.transform = transform # Affine transform
            self.height, self.width = data.shape
            # self.bounds = rasterio.coords.BoundingBox(transform.c, transform.f + transform.e * self.height, transform.c + transform.a * self.width, transform.f) # Requires rasterio

        def read(self, window):
            # simplified read for testing
            return self.data[window.row_off:window.row_off+window.height, window.col_off:window.col_off+window.width].reshape(1, window.height, window.width)
        
        def index(self, x, y):
            # simplified index for testing
            # col, row = ~self.transform * (x,y) # Requires rasterio.Affine
            # return int(row), int(col)
            pass # Placeholder

    def test_ray_dsm_intersection_optimized_correctness(mock_dsm_data):
        # Arrange
        # P_sensor_world, d_world (ray origin and direction)
        # mock_dsm = MockDSM(...) 
        
        # Act
        # intersection_point, success_flag = calculate_ray_dsm_intersection(P_sensor_world, d_world, mock_dsm, ...)
        
        # Assert
        # Based on the mock_dsm_data and ray, assert correct intersection_point and success_flag
        pass
    ```

### Test Case 2.4: Performance Benchmark for Georeferencing Step
-   **Description**: Measure and compare the execution time of the georeferencing process before and after vectorization.
-   **Module**: [`georeference_hsi_pixels.py`](georeference_hsi_pixels.py:1)
-   **Function(s) to Test**: The main georeferencing function/loop.
-   **Inputs**:
    -   A representative sample HSI dataset (e.g., one full HSI file).
    -   Corresponding pose data and DSM.
-   **Actions**:
    -   Run the georeferencing process using `pytest-benchmark` or `timeit`.
-   **Expected Outputs/Behavior**: A significant reduction in execution time for the vectorized version.
-   **Acceptance Criteria**: Performance improvement meets target (e.g., >5x speedup, or as per expected improvements). Benchmark results are recorded.
-   **Test Scaffolding (Python/pytest-benchmark)**:
    ```python
    # Assuming georeference_hsi_pixels.run_georeferencing is the main entry point
    
    def test_georeferencing_performance_vectorized(benchmark, setup_sample_dataset_for_georef):
        # Arrange
        # config, hsi_file_path, poses_data, dsm_path = setup_sample_dataset_for_georef
        
        # Act & Assert (benchmark runs the function multiple times)
        # result = benchmark(georeference_hsi_pixels.run_georeferencing, config, hsi_file_path, poses_data, dsm_path)
        # No direct assert here, benchmark handles stats. Comparison is vs. a baseline.
        pass
        
    # A baseline test for the old version would be needed for comparison if kept separately
    # def test_georeferencing_performance_original(benchmark, setup_sample_dataset_for_georef):
    #     benchmark(georeference_hsi_pixels.run_georeferencing_original, ...) # if original is preserved
    #     pass
    ```

### Test Case 2.5: Memory Usage Check (Conceptual)
-   **Description**: Ensure memory usage remains manageable, especially for large HSI datasets.
-   **Module**: [`georeference_hsi_pixels.py`](georeference_hsi_pixels.py:1)
-   **Function(s) to Test**: Main georeferencing function.
-   **Inputs**: Large HSI dataset.
-   **Actions**: Run georeferencing while monitoring memory (e.g., using `memory_profiler`).
-   **Expected Outputs/Behavior**: Peak memory usage is within acceptable limits. No excessive memory growth.
-   **Acceptance Criteria**: Memory usage does not exceed a predefined threshold or scales reasonably with input size.
-   **Test Scaffolding**: This is harder to automate in typical unit/integration test suites. Often done with dedicated profiling runs.
    ```python
    # Example using memory_profiler (run separately, not typically in pytest suite)
    # from memory_profiler import profile
    #
    # @profile
    # def run_georef_for_memory_profile():
    #     # Setup and call georeference_hsi_pixels.run_georeferencing with large data
    #     pass
    #
    # if __name__ == '__main__':
    #    run_georef_for_memory_profile()
    ```

## Based on Prompt [LS2_3]: Centralized Configuration

**Objective**: Parse `config.toml` once in [`main_pipeline.py`](main_pipeline.py:1) and pass configuration to sub-modules.

### Test Case 3.1: `main_pipeline.py` Loads Config
-   **Description**: Verify [`main_pipeline.py`](main_pipeline.py:1) loads `config.toml` correctly.
-   **Module**: [`main_pipeline.py`](main_pipeline.py:1)
-   **Function(s) to Test**: `run_complete_pipeline` (or a new config loading utility within it).
-   **Inputs**:
    -   Mock `config.toml` file with various sections and parameters.
-   **Actions**:
    -   Call `run_complete_pipeline` (or the part that loads config).
-   **Expected Outputs/Behavior**: The loaded config object in `main_pipeline.py` accurately reflects the content of the mock `config.toml`.
-   **Acceptance Criteria**: Parsed configuration data matches the mock file content.
-   **Test Scaffolding (Python/pytest)**:
    ```python
    from pathlib import Path
    import toml
    # import main_pipeline 

    def test_main_pipeline_loads_config(mocker, tmp_path):
        # Arrange
        mock_config_content = {
            "paths": {"output_dir": "/tmp/output"},
            "georeferencing": {"dsm_path": "/tmp/dsm.tif"}
        }
        config_file = tmp_path / "mock_config.toml"
        with open(config_file, "w") as f:
            toml.dump(mock_config_content, f)
        
        # Mock sub-module calls to prevent full pipeline run
        mocker.patch('main_pipeline.run_consolidation', return_value=True)
        # ... mock other run_* calls

        # Act
        # loaded_config = main_pipeline.load_pipeline_config(str(config_file)) # Assuming a helper
        # OR: Inspect config object after main_pipeline.run_complete_pipeline(str(config_file))
        
        # Assert
        # assert loaded_config["paths"]["output_dir"] == "/tmp/output"
        # assert loaded_config["georeferencing"]["dsm_path"] == "/tmp/dsm.tif"
        pass
    ```

### Test Case 3.2: Sub-modules Receive Config as Arguments
-   **Description**: Verify that sub-modules (e.g., `georeference_hsi_pixels.run_georeferencing`) correctly receive configuration data as arguments.
-   **Module**: [`main_pipeline.py`](main_pipeline.py:1) (caller), and sub-modules like [`georeference_hsi_pixels.py`](georeference_hsi_pixels.py:1) (callee).
-   **Function(s) to Test**: `run_*` functions in [`main_pipeline.py`](main_pipeline.py:1) and their counterparts in sub-modules.
-   **Inputs**:
    -   A sample loaded config object.
-   **Actions**:
    -   Call a `run_*` function from `main_pipeline.py` (e.g., `main_pipeline.run_georeferencing_stage`).
    -   The sub-module's `run_georeferencing` function will be called internally.
-   **Expected Outputs/Behavior**: The sub-module's function is called with the expected config object or parameters.
-   **Acceptance Criteria**: Mocks of sub-module functions confirm they were called with the correct config data.
-   **Test Scaffolding (Python/pytest)**:
    ```python
    # import main_pipeline
    # import georeference_hsi_pixels # as geo_mod
    # import synchronize_hsi_webodm # as sync_mod
    # ... etc.

    def test_submodules_receive_config_via_args(mocker):
        # Arrange
        mock_config = {"key": "value", "georef_specific": {"param": 1}}
        
        # Mock the actual processing within sub-modules, just check if they are called correctly
        mock_geo_run = mocker.patch('georeference_hsi_pixels.run_georeferencing')
        # mock_sync_run = mocker.patch('synchronize_hsi_webodm.run_synchronization')
        
        # Act
        # main_pipeline.run_complete_pipeline(config_object=mock_config) # If main_pipeline is refactored to take object
        # OR: main_pipeline.call_georeferencing_submodule(mock_config, ...)
        
        # Assert
        # mock_geo_run.assert_called_once()
        # args, kwargs = mock_geo_run.call_args
        # assert kwargs.get('config') == mock_config['georef_specific'] # or however it's passed
        # OR: assert args[0] == mock_config # if passed as first arg
        pass
    ```

### Test Case 3.3: Sub-modules Use Passed Config (No Internal Loading)
-   **Description**: Verify sub-modules use the passed config data and do not attempt to load `config.toml` themselves.
-   **Module**: [`georeference_hsi_pixels.py`](georeference_hsi_pixels.py:1), [`synchronize_hsi_webodm.py`](synchronize_hsi_webodm.py:1), etc.
-   **Function(s) to Test**: `run_*` functions within sub-modules.
-   **Inputs**:
    -   Mock config data passed as an argument.
-   **Actions**:
    -   Call the sub-module's `run_*` function directly with mock config.
    -   Mock `toml.load` or file open calls within the sub-module to ensure they are NOT called.
-   **Expected Outputs/Behavior**: The sub-module executes its logic using the provided config argument. `toml.load` (or equivalent for config file reading) within the sub-module is not called.
-   **Acceptance Criteria**: `toml.load` mock in sub-module scope is not called. Sub-module behaves as expected with passed config.
-   **Test Scaffolding (Python/pytest)**:
    ```python
    # import georeference_hsi_pixels as geo_mod

    def test_submodule_uses_passed_config_not_internal_load(mocker):
        # Arrange
        passed_config_params = {"dsm_path": "fake/dsm.tif", "output_file": "out.csv"}
        
        # Mock toml.load specifically in the context of the submodule to ensure it's not called
        mock_toml_load = mocker.patch('georeference_hsi_pixels.toml.load') # Adjust path if toml is imported differently
        mocker.patch('builtins.open', mocker.mock_open()) # Also mock open if it tries to read config
        
        # Mock other dependencies of the run_georeferencing function to isolate config usage
        mocker.patch('georeference_hsi_pixels.some_internal_processing_func')

        # Act
        try:
            # geo_mod.run_georeferencing(config_params=passed_config_params, hsi_file=..., poses_file=...)
            pass # Call the actual submodule function
        except Exception as e:
            # Depending on mocks, it might not fully run, or fail if mocks are incomplete
            print(f"Submodule execution raised: {e}")

        # Assert
        mock_toml_load.assert_not_called()
        # Further asserts: check if internal_processing_func was called with data derived from passed_config_params
        # e.g., georeference_hsi_pixels.some_internal_processing_func.assert_called_with(dsm_path="fake/dsm.tif", ...)
        pass
    ```

## Based on Prompt [LS2_4]: Standardized Logging and Error Handling

**Objective**: Implement `logging` module, custom exceptions, and enforce English.

### Test Case 4.1: Logging Integration - Message Emission
-   **Description**: Verify that appropriate log messages are emitted at different log levels for key operations.
-   **Module**: All Python scripts.
-   **Function(s) to Test**: Various functions performing key operations.
-   **Inputs**: Trigger conditions for INFO, DEBUG, WARNING, ERROR logs.
-   **Actions**: Execute functions that should produce logs.
-   **Expected Outputs/Behavior**: Correctly formatted log messages appear in `caplog` output with the expected level and content.
-   **Acceptance Criteria**: Log records match expected messages and levels.
-   **Test Scaffolding (Python/pytest)**:
    ```python
    import logging
    # import main_pipeline # or other modules

    def test_log_message_emission_info(caplog):
        # Arrange
        # main_pipeline.setup_logging(level=logging.INFO) # Ensure logger is configured
        caplog.set_level(logging.INFO)
        
        # Act
        # main_pipeline.some_function_that_logs_info("test_data")
        
        # Assert
        # assert "Informative message about test_data" in caplog.text
        # assert any(record.levelname == 'INFO' and "Expected message" in record.message for record in caplog.records)
        pass

    def test_log_message_emission_error(caplog, mocker):
        # Arrange
        caplog.set_level(logging.ERROR)
        mocker.patch('main_pipeline.some_problematic_operation', side_effect=ValueError("test error"))

        # Act
        # try:
        #     main_pipeline.function_that_handles_error()
        # except ValueError:
        #     pass # Error expected and handled, logging should occur
            
        # Assert
        # assert "Error during problematic_operation: test error" in caplog.text
        # assert any(record.levelname == 'ERROR' and "test error" in record.message for record in caplog.records)
        pass
    ```

### Test Case 4.2: Custom Exception Raising
-   **Description**: Verify that custom exceptions (e.g., `PipelineConfigError`, `HSIDataError`) are raised correctly under specific error conditions.
-   **Module**: Sub-modules like [`georeference_hsi_pixels.py`](georeference_hsi_pixels.py:1).
-   **Function(s) to Test**: Functions responsible for operations that can fail in specific ways.
-   **Inputs**:
    -   Invalid config data (for `PipelineConfigError`).
    -   Corrupted HSI file (for `HSIDataError`).
-   **Actions**:
    -   Call functions with inputs designed to trigger these errors.
-   **Expected Outputs/Behavior**: The specific custom exception is raised.
-   **Acceptance Criteria**: `pytest.raises` successfully catches the expected custom exception.
-   **Test Scaffolding (Python/pytest)**:
    ```python
    import pytest
    # from main_pipeline import PipelineConfigError, HSIDataError # Assuming exceptions are defined/accessible
    # import georeference_hsi_pixels as geo_mod

    # Placeholder for custom exceptions (should be defined in the actual code)
    class PipelineConfigError(Exception): pass
    class HSIDataError(Exception): pass

    def test_raises_pipeline_config_error_on_bad_config(mocker):
        # Arrange
        # Mock a function in georeference_hsi_pixels that processes config
        # and is supposed to raise PipelineConfigError
        # def func_processing_config(config):
        # if "required_key" not in config:
        # raise PipelineConfigError("Missing required_key")
        # return True
        # mocker.patch('georeference_hsi_pixels.process_config_section', side_effect=PipelineConfigError("Bad config"))
        
        # Act & Assert
        # with pytest.raises(PipelineConfigError, match="Bad config"):
        #    geo_mod.run_georeferencing(config_params={"invalid_key": "value"}, ...)
        pass

    def test_raises_hsi_data_error_on_corrupt_file(mocker):
        # Arrange
        # Mock header parsing to simulate corrupt data
        # mocker.patch('georeference_hsi_pixels.parse_hsi_header', side_effect=HSIDataError("Corrupt HSI header"))
        
        # Act & Assert
        # with pytest.raises(HSIDataError, match="Corrupt HSI header"):
        #    geo_mod.run_georeferencing(config_params={...}, hsi_file_path="dummy_corrupt.hdr", ...)
        pass
    ```

### Test Case 4.3: `main_pipeline.py` Catches Custom Exceptions
-   **Description**: Verify [`main_pipeline.py`](main_pipeline.py:1) correctly catches custom exceptions from sub-module calls and logs them.
-   **Module**: [`main_pipeline.py`](main_pipeline.py:1)
-   **Function(s) to Test**: `run_complete_pipeline` or specific stage-running functions within it.
-   **Inputs**: Mock a sub-module to raise a specific custom exception.
-   **Actions**: Call `run_complete_pipeline` (or the relevant part).
-   **Expected Outputs/Behavior**:
    -   The custom exception is caught.
    -   An appropriate error message is logged by `main_pipeline.py`.
    -   Pipeline terminates gracefully or attempts recovery as designed.
-   **Acceptance Criteria**: `caplog` shows `main_pipeline.py` logged the caught custom exception. The pipeline doesn't crash with an unhandled exception.
-   **Test Scaffolding (Python/pytest)**:
    ```python
    # import main_pipeline
    # from main_pipeline import GeoreferencingError # Example custom exception

    def test_main_pipeline_catches_custom_exceptions(mocker, caplog):
        # Arrange
        caplog.set_level(logging.ERROR)
        # Mock a submodule function to raise a custom exception
        # mocker.patch('georeference_hsi_pixels.run_georeferencing', side_effect=GeoreferencingError("DSM not found"))
        # Placeholder for actual exception name
        class GeoreferencingError(Exception): pass 
        mocker.patch('main_pipeline.run_georeferencing', side_effect=GeoreferencingError("DSM not found")) # If main_pipeline calls it directly
        mocker.patch('main_pipeline.run_consolidation', return_value=True) # Mock other stages
        mocker.patch('main_pipeline.run_synchronization', return_value=True)
        mocker.patch('main_pipeline.run_plotting', return_value=True)


        # Act
        # result = main_pipeline.run_complete_pipeline(config_path="dummy_config.toml")
        
        # Assert
        # assert result is False # Or whatever indicates pipeline failure
        # assert "GeoreferencingError: DSM not found" in caplog.text # Check for logged error
        # assert "Pipeline execution failed at georeferencing stage due to: DSM not found" in caplog.text # Example log from main_pipeline
        pass
    ```

### Test Case 4.4: English Language Enforcement (Conceptual)
-   **Description**: Verify all code elements (comments, identifiers, logs) are in English.
-   **Module**: All Python scripts.
-   **Function(s) to Test**: N/A (Static code review/linting).
-   **Inputs**: N/A.
-   **Actions**: Review code and log outputs.
-   **Expected Outputs/Behavior**: No German language elements.
-   **Acceptance Criteria**: Codebase is consistently in English.
-   **Test Scaffolding**: Manual review or automated linters with language detection (if feasible).

## Based on Prompt [LS2_5]: Test Coverage Increase

**Objective**: Significantly increase unit and integration test coverage across the pipeline. Many of these tests are foundations for the above prompts as well.

### Unit Tests for [`georeference_hsi_pixels.py`](georeference_hsi_pixels.py:1)

#### Test Case 5.1.1: `parse_hsi_header`
-   **Description**: Test parsing of various HSI header fields.
-   **Inputs**: Mock HSI header file content (strings) with different valid and some invalid/missing fields.
-   **Actions**: Call `parse_hsi_header`.
-   **Expected Outputs**: Correctly parsed dictionary of header values, or appropriate errors/defaults for malformed input.
-   **Acceptance Criteria**: Parsed values match mock input. Handles missing keys gracefully if designed to.
-   **Scaffolding**:
    ```python
    # from georeference_hsi_pixels import parse_hsi_header
    def test_parse_hsi_header_valid(mocker):
        mock_content = "samples = 640\nlines = 480\nwavelength = 500.2, 505.5, ..."
        mocker.patch('builtins.open', mocker.mock_open(read_data=mock_content))
        # header_data = parse_hsi_header("dummy_path.hdr")
        # assert header_data['samples'] == 640
        pass
    ```

#### Test Case 5.1.2: `calculate_sensor_view_vector`
-   **Description**: Test calculation of sensor view vector for different pixel indices (center, edges).
-   **Inputs**: Pixel index (x_idx), image width, IFOV.
-   **Actions**: Call `calculate_sensor_view_vector`.
-   **Expected Outputs**: Correct 3D unit vector.
-   **Acceptance Criteria**: Vector components match theoretical calculations for given inputs.
-   **Scaffolding**:
    ```python
    # from georeference_hsi_pixels import calculate_sensor_view_vector
    def test_calculate_sensor_view_vector_center_pixel():
        # width = 640; ifov_rad = 0.001
        # vector = calculate_sensor_view_vector(319.5, width, ifov_rad) # Assuming 0-indexed, center between 319 and 320
        # np.testing.assert_array_almost_equal(vector, [0, 0, -1]) # Or whatever convention is used for sensor frame
        pass
    ```

#### Test Case 5.1.3: `calculate_ray_dsm_intersection` (Covered by 2.3, expand with more DSM scenarios)
-   **Description**: Test with mock/simple DSM, known intersection points, edge cases (ray parallel to DSM, ray starts inside DSM).
-   **Acceptance Criteria**: Correct intersection point and success status.

#### Test Case 5.1.4: `transform_enu_to_latlonalt` (and inverse)
-   **Description**: Test coordinate transformation with known ENU, LatLonAlt pairs and a reference origin.
-   **Inputs**: ENU coordinates, reference LatLonAlt origin.
-   **Actions**: Call transformation function.
-   **Expected Outputs**: Correct LatLonAlt. Test inverse if applicable.
-   **Acceptance Criteria**: Transformed coordinates match pre-calculated known values.
-   **Scaffolding**:
    ```python
    # from georeference_hsi_pixels import transform_enu_to_latlonalt # Assuming it exists
    def test_transform_enu_to_latlonalt():
        # enu_coords = np.array([10, 20, 5])
        # ref_lat, ref_lon, ref_alt = 45.0, 10.0, 100.0
        # lat, lon, alt = transform_enu_to_latlonalt(enu_coords[0], enu_coords[1], enu_coords[2], ref_lat, ref_lon, ref_alt)
        # Assert known expected lat, lon, alt
        pass
    ```

#### Test Case 5.1.5: Sensor Model Angle Interpretation
-   **Description**: Test correct handling of `vinkelx_deg`, `vinkely_deg` (units, scale/offset).
-   **Inputs**: Sample angle values from mock header.
-   **Actions**: Part of a larger function that uses these angles (e.g., in boresight or view vector calculation).
-   **Expected Outputs**: Internal representation of angles (e.g., radians) is correct.
-   **Acceptance Criteria**: Final calculations involving these angles are correct.

#### Test Case 5.1.6: Boresight Matrix Construction and Application
-   **Description**: Test the construction of the boresight rotation matrix and its application.
-   **Inputs**: Boresight angles (roll, pitch, yaw).
-   **Actions**: Construct matrix, apply to a known vector.
-   **Expected Outputs**: Correctly rotated vector.
-   **Acceptance Criteria**: Matrix elements and rotated vector match theoretical values.

### Unit Tests for [`synchronize_hsi_webodm.py`](synchronize_hsi_webodm.py:1)

#### Test Case 5.2.1: `parse_hsi_timestamps_from_hdr`
-   **Description**: Test parsing of HSI timestamps from mock header data.
-   **Inputs**: Mock HSI header file content with timestamp information.
-   **Actions**: Call `parse_hsi_timestamps_from_hdr`.
-   **Expected Outputs**: List/array of correctly parsed timestamps.
-   **Acceptance Criteria**: Timestamps match input, correct data types.

#### Test Case 5.2.2: `interpolate_poses_to_hsi_timestamps`
-   **Description**: Test pose interpolation (linear for position, Slerp for quaternions).
-   **Inputs**: Sample pose data (timestamps, positions, orientations/quaternions), target HSI timestamps.
-   **Actions**: Call `interpolate_poses_to_hsi_timestamps`.
-   **Expected Outputs**: Correctly interpolated poses at HSI timestamps.
-   **Acceptance Criteria**: Interpolated values match known intermediate values for simple cases. Slerp properties are maintained.

### Unit Tests for `create_consolidated_webodm_poses.py`

#### Test Case 5.3.1: Core Logic for Consolidating Poses
-   **Description**: Test parsing of WebODM/MMS data and consolidation into the target format.
-   **Inputs**: Mock WebODM `shots.geojson`, MMS pose file.
-   **Actions**: Run the consolidation script/main function.
-   **Expected Outputs**: A consolidated pose file in the correct format with expected values.
-   **Acceptance Criteria**: Output file content matches expected consolidated data.

### Integration Tests for Pipeline Stages (Test Case 5.4)

-   **Description**: Test sequential pairs of pipeline steps (e.g., consolidation -> synchronization, synchronization -> georeferencing).
-   **Inputs**: Small, well-defined input datasets for the first stage in the pair.
-   **Actions**: Run the two stages sequentially.
-   **Expected Outputs**: Intermediate and final outputs are correct and consistent.
-   **Acceptance Criteria**: Output of stage 1 is correctly consumed by stage 2, and stage 2 output is as expected.
-   **Scaffolding**:
    ```python
    def test_integration_consolidation_to_synchronization(tmp_path):
        # Arrange: Setup mock input files for consolidation
        # Act:
        # consolidated_output_path = main_pipeline.run_consolidation(...)
        # synchronized_output_path = main_pipeline.run_synchronization(input_poses=consolidated_output_path, ...)
        # Assert: Check content of synchronized_output_path
        pass
    ```

### Configuration and Input Validation Tests (Test Case 5.5)

-   **Description**: Test loading/validation of `config.toml` parameters and input files.
-   **(Covered by Test Case 3.1, 4.2 for config; extend for various input file validations like missing files, incorrect formats).**
-   **Acceptance Criteria**: Appropriate errors/warnings for invalid configs/inputs. Correct parsing for valid ones.

### Coordinate System and Transformation Tests (Test Case 5.6)

-   **Description**: Verify correctness of all key coordinate transformations (IMU to body, body to sensor, sensor to world, ENU to LatLonAlt) using known vectors. Ensure consistent quaternion usage.
-   **(Partially covered by 5.1.4, 5.1.6. Expand to cover all transformations explicitly with test vectors.)**
-   **Acceptance Criteria**: Transformed vectors match pre-calculated, known correct values. Quaternion operations are validated.

### Documentation Alignment (Test Case 5.7)
-   **Description**: Ensure [`main_pipeline_documentation.md`](main_pipeline_documentation.md:1) reflects tested behavior.
-   **Acceptance Criteria**: Documentation is consistent with implemented and tested logic (e.g., quaternion usage, boresight definition). (Manual review aided by tests).

### Coverage Goal (Test Case 5.8)
-   **Description**: Achieve at least 50% line coverage.
-   **Actions**: Run tests with `coverage.py`.
-   **Acceptance Criteria**: Coverage report shows >= 50% line coverage. (Meta-goal).