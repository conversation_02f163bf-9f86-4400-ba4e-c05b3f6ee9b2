"""
Unit tests for the updated synchronize_hsi_webodm module.

This module tests the LS2 improvements including centralized configuration,
logging integration, and custom exception handling.
"""

import pytest
import numpy as np
import pandas as pd
import tempfile
import os
from pathlib import Path
from unittest.mock import patch, MagicMock
from scipy.spatial.transform import Rotation

# Import the modules to test
from src.hsi_pipeline.synchronize_hsi_webodm import (
    run_synchronization, parse_hdr_file, load_hsi_data,
    load_webodm_data, interpolate_pose, convert_hsi_timestamp_to_ns
)
from src.hsi_pipeline.pipeline_exceptions import (
    PipelineConfigError, HSIDataError, SynchronizationError, InputDataError
)


class TestSynchronizationLS2Updates:
    """Test cases for LS2 updates to synchronization module."""
    
    def test_function_signature_accepts_config_dict(self):
        """Test that run_synchronization accepts config dict instead of config_path string."""
        # Arrange
        config = {
            'paths': {
                'hsi_data_directory': 'test_dir',
                'hsi_base_filename': 'test_hsi',
                'output_directory': 'output',
                'consolidated_webodm_poses_csv': 'webodm.csv',
                'hsi_poses_csv': 'hsi_poses.csv'
            }
        }
        
        # Act & Assert - Should not raise TypeError for accepting dict
        # We'll mock the file operations to avoid actual file I/O
        with patch('src.hsi_pipeline.synchronize_hsi_webodm.os.makedirs'), \
             patch('src.hsi_pipeline.synchronize_hsi_webodm.parse_hdr_file', side_effect=InputDataError("Test")):
            
            with pytest.raises(InputDataError):  # Expected due to mocked error
                run_synchronization(config)
    
    def test_missing_config_key_raises_pipeline_config_error(self):
        """Test that missing configuration keys raise PipelineConfigError."""
        # Arrange
        incomplete_config = {
            'paths': {
                'hsi_data_directory': 'test_dir'
                # Missing other required keys
            }
        }
        
        # Act & Assert
        with pytest.raises(PipelineConfigError, match="Missing configuration key"):
            run_synchronization(incomplete_config)


class TestHelperFunctions:
    """Test cases for helper functions."""
    
    def test_convert_hsi_timestamp_to_ns(self):
        """Test timestamp conversion from microseconds to nanoseconds."""
        # Arrange
        timestamp_us = "1234567890"
        
        # Act
        result = convert_hsi_timestamp_to_ns(timestamp_us)
        
        # Assert
        assert result == 1234567890000  # microseconds * 1000 = nanoseconds
    
    def test_parse_hdr_file_success(self, tmp_path):
        """Test successful HDR file parsing."""
        # Arrange
        hdr_file = tmp_path / "test.hdr"
        hdr_content = """ENVI
lines = 1000
OffsetBetweenMainAntennaAndTargetPoint (x,y,z) = {120, 80, -50}
"""
        hdr_file.write_text(hdr_content)
        
        # Act
        result = parse_hdr_file(str(hdr_file))
        
        # Assert
        assert result is not None
        assert result["lines"] == 1000
        assert result["lever_arm_xyz_mm"] == [120, 80, -50]
    
    def test_parse_hdr_file_missing_file(self):
        """Test HDR file parsing with missing file."""
        # Act & Assert
        with pytest.raises(InputDataError, match="Header file not found"):
            parse_hdr_file("nonexistent.hdr")
    
    def test_load_hsi_data_success(self, tmp_path):
        """Test successful HSI sync data loading."""
        # Arrange
        sync_file = tmp_path / "test_sync.txt"
        sync_content = """Frame/Line	Timestamp
1	1234567890
2	1234567891
3	1234567892
"""
        sync_file.write_text(sync_content)
        
        # Act
        result = load_hsi_data(str(sync_file), 3)
        
        # Assert
        assert len(result) == 3
        assert result[0] == (0, 1234567890000)  # Corrected index, converted timestamp
        assert result[1] == (1, 1234567891000)
        assert result[2] == (2, 1234567892000)
    
    def test_load_hsi_data_invalid_format(self, tmp_path):
        """Test HSI sync data loading with invalid format."""
        # Arrange
        sync_file = tmp_path / "invalid_sync.txt"
        sync_file.write_text("Invalid content")
        
        # Act & Assert
        with pytest.raises(HSIDataError, match="Invalid HSI sync file format"):
            load_hsi_data(str(sync_file), 3)
    
    def test_load_webodm_data_success(self, tmp_path):
        """Test successful WebODM data loading."""
        # Arrange
        csv_file = tmp_path / "webodm.csv"
        df = pd.DataFrame({
            'haip_timestamp_ns': [1234567890000, 1234567891000],
            'pos_x': [100.0, 101.0],
            'pos_y': [200.0, 201.0],
            'pos_z': [50.0, 51.0],
            'rot_1': [0.1, 0.2],
            'rot_2': [0.2, 0.3],
            'rot_3': [0.3, 0.4],
            'image_filename': ['img1.jpg', 'img2.jpg']
        })
        df.to_csv(csv_file, index=False)
        
        # Act
        result = load_webodm_data(str(csv_file))
        
        # Assert
        assert len(result) == 2
        assert result[0]['timestamp_ns'] == 1234567890000
        assert np.array_equal(result[0]['translation'], np.array([100.0, 200.0, 50.0]))
        assert np.array_equal(result[0]['rotation_vec'], np.array([0.1, 0.2, 0.3]))
    
    def test_load_webodm_data_missing_columns(self, tmp_path):
        """Test WebODM data loading with missing columns."""
        # Arrange
        csv_file = tmp_path / "incomplete_webodm.csv"
        df = pd.DataFrame({
            'haip_timestamp_ns': [1234567890000],
            'pos_x': [100.0]
            # Missing other required columns
        })
        df.to_csv(csv_file, index=False)
        
        # Act & Assert
        with pytest.raises(SynchronizationError, match="Missing required columns"):
            load_webodm_data(str(csv_file))
    
    def test_interpolate_pose_exact_match(self):
        """Test pose interpolation with exact timestamp match."""
        # Arrange
        webodm_poses = [
            {
                'timestamp_ns': 1000,
                'translation': np.array([1.0, 2.0, 3.0]),
                'rotation_vec': np.array([0.1, 0.2, 0.3])
            },
            {
                'timestamp_ns': 2000,
                'translation': np.array([2.0, 3.0, 4.0]),
                'rotation_vec': np.array([0.2, 0.3, 0.4])
            }
        ]
        
        # Act
        translation, rotation, delta_prev, delta_next = interpolate_pose(1000, webodm_poses)
        
        # Assert
        np.testing.assert_array_equal(translation, np.array([1.0, 2.0, 3.0]))
        assert delta_prev == 0.0
        assert delta_next == 1000.0
    
    def test_interpolate_pose_interpolation(self):
        """Test pose interpolation between two poses."""
        # Arrange
        webodm_poses = [
            {
                'timestamp_ns': 1000,
                'translation': np.array([0.0, 0.0, 0.0]),
                'rotation_vec': np.array([0.0, 0.0, 0.0])
            },
            {
                'timestamp_ns': 3000,
                'translation': np.array([2.0, 2.0, 2.0]),
                'rotation_vec': np.array([0.2, 0.2, 0.2])
            }
        ]
        
        # Act - interpolate at midpoint
        translation, rotation, delta_prev, delta_next = interpolate_pose(2000, webodm_poses)
        
        # Assert
        expected_translation = np.array([1.0, 1.0, 1.0])  # Midpoint
        np.testing.assert_array_almost_equal(translation, expected_translation)
        assert delta_prev == 1000.0
        assert delta_next == 1000.0
    
    def test_interpolate_pose_extrapolation_before(self):
        """Test pose extrapolation before first timestamp."""
        # Arrange
        webodm_poses = [
            {
                'timestamp_ns': 2000,
                'translation': np.array([1.0, 2.0, 3.0]),
                'rotation_vec': np.array([0.1, 0.2, 0.3])
            }
        ]
        
        # Act
        translation, rotation, delta_prev, delta_next = interpolate_pose(1000, webodm_poses)
        
        # Assert
        np.testing.assert_array_equal(translation, np.array([1.0, 2.0, 3.0]))
        assert np.isnan(delta_prev)
        assert delta_next == 1000.0
    
    def test_interpolate_pose_extrapolation_after(self):
        """Test pose extrapolation after last timestamp."""
        # Arrange
        webodm_poses = [
            {
                'timestamp_ns': 1000,
                'translation': np.array([1.0, 2.0, 3.0]),
                'rotation_vec': np.array([0.1, 0.2, 0.3])
            }
        ]
        
        # Act
        translation, rotation, delta_prev, delta_next = interpolate_pose(2000, webodm_poses)
        
        # Assert
        np.testing.assert_array_equal(translation, np.array([1.0, 2.0, 3.0]))
        assert delta_prev == 1000.0
        assert np.isnan(delta_next)


class TestLoggingIntegration:
    """Test cases for logging integration."""
    
    @patch('src.hsi_pipeline.synchronize_hsi_webodm.get_logger')
    def test_logging_integration(self, mock_get_logger):
        """Test that logging is properly integrated."""
        # Arrange
        mock_logger = MagicMock()
        mock_get_logger.return_value = mock_logger
        
        config = {
            'paths': {
                'hsi_data_directory': 'test_dir',
                'hsi_base_filename': 'test_hsi',
                'output_directory': 'output',
                'consolidated_webodm_poses_csv': 'webodm.csv',
                'hsi_poses_csv': 'hsi_poses.csv'
            }
        }
        
        with patch('src.hsi_pipeline.synchronize_hsi_webodm.os.makedirs'), \
             patch('src.hsi_pipeline.synchronize_hsi_webodm.parse_hdr_file', side_effect=InputDataError("Test")):
            
            # Act
            with pytest.raises(InputDataError):
                run_synchronization(config)
        
        # Assert
        mock_logger.info.assert_called()
        mock_logger.error.assert_called()


if __name__ == "__main__":
    # Run tests with pytest
    pytest.main([__file__, "-v"])
